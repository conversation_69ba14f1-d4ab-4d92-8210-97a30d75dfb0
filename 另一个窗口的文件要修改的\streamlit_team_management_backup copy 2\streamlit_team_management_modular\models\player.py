#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球员数据模型
Player Data Model

定义球员相关的数据结构
"""

import uuid
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, Optional


@dataclass
class PlayerInfo:
    """球员基本信息"""
    name: str
    jersey_number: str
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保球衣号码是字符串
        self.jersey_number = str(self.jersey_number)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'jersey_number': self.jersey_number
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PlayerInfo':
        """从字典创建实例"""
        return cls(
            name=data.get('name', ''),
            jersey_number=str(data.get('jersey_number', ''))
        )


@dataclass
class Player:
    """球员完整数据模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    jersey_number: str = ""
    photo: Optional[str] = None
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保球衣号码是字符串
        self.jersey_number = str(self.jersey_number)
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        return f"{self.name} (#{self.jersey_number})"
    
    @property
    def has_photo(self) -> bool:
        """是否有照片"""
        return self.photo is not None and self.photo.strip() != ""
    
    def update_info(self, name: str = None, jersey_number: str = None) -> None:
        """更新球员信息"""
        if name is not None:
            self.name = name
        if jersey_number is not None:
            self.jersey_number = str(jersey_number)
        self.updated_at = datetime.now().isoformat()
    
    def set_photo(self, photo_filename: str) -> None:
        """设置照片"""
        self.photo = photo_filename
        self.updated_at = datetime.now().isoformat()
    
    def remove_photo(self) -> None:
        """移除照片"""
        self.photo = None
        self.updated_at = datetime.now().isoformat()
    
    def get_ai_tags(self, team_name: str) -> Dict[str, str]:
        """获取AI标签"""
        return {
            'person_id': f"{team_name}_{self.jersey_number}_{self.name}",
            'display_name': self.display_name,
            'team': team_name
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'jersey_number': self.jersey_number,
            'photo': self.photo,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Player':
        """从字典创建实例"""
        return cls(
            id=data.get('id', str(uuid.uuid4())),
            name=data.get('name', ''),
            jersey_number=str(data.get('jersey_number', '')),
            photo=data.get('photo'),
            created_at=data.get('created_at', datetime.now().isoformat()),
            updated_at=data.get('updated_at', datetime.now().isoformat())
        )
    
    @classmethod
    def create_new(cls, name: str, jersey_number: str, photo: str = None) -> 'Player':
        """创建新球员"""
        return cls(
            name=name,
            jersey_number=str(jersey_number),
            photo=photo
        )
