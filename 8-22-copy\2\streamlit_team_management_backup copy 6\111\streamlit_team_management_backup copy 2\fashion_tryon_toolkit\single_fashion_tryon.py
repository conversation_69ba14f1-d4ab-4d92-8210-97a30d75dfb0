#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单张照片时尚换装工具
Single Photo Fashion Try-On Tool

使用方法:
python single_fashion_tryon.py --model_image path/to/model.jpg --clothes_image path/to/clothes.png

或者直接运行脚本，按提示输入文件路径
"""

import requests
import time
import os
import argparse
from pathlib import Path
from PIL import Image
from config import *

def create_output_dirs():
    """创建输出目录"""
    for dir_name in OUTPUT_DIRS.values():
        os.makedirs(dir_name, exist_ok=True)

def step1_fashion_tryon(model_image, clothes_image):
    """步骤1: 302.AI-ComfyUI 换装"""
    print("\n" + "="*60)
    print("🎯 步骤1: 302.AI-ComfyUI 换装")
    print("="*60)
    
    if not os.path.exists(model_image):
        print(f"❌ 模特图片不存在: {model_image}")
        return None
    
    if not os.path.exists(clothes_image):
        print(f"❌ 服装图片不存在: {clothes_image}")
        return None
    
    print(f"📸 模特图片: {model_image}")
    print(f"👕 服装图片: {clothes_image}")
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/create-task"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        with open(model_image, 'rb') as model_file, open(clothes_image, 'rb') as clothes_file:
            files = {
                'modelImageFile': (os.path.basename(model_image), model_file, 'image/jpeg'),
                'clothesImageFile': (os.path.basename(clothes_image), clothes_file, 'image/png')
            }
            
            data = FASHION_TRYON_CONFIG
            
            print("📤 发送换装任务请求...")
            response = requests.post(url, headers=headers, files=files, data=data, 
                                   timeout=TIMEOUT_CONFIG["request_timeout"])
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None
    
    print(f"📊 响应状态码: {response.status_code}")
    
    if response.status_code in [200, 201]:
        try:
            result = response.json()
            if result.get('code') == 200 and 'data' in result:
                task_id = result['data']['taskId']
                print(f"✅ 换装任务创建成功！任务ID: {task_id}")
                return wait_for_fashion_task(task_id)
            else:
                print(f"❌ 任务创建失败: {result}")
                return None
        except Exception as e:
            print(f"❌ 解析响应失败: {e}")
            return None
    else:
        print(f"❌ API请求失败: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        return None

def wait_for_fashion_task(task_id):
    """等待换装任务完成"""
    print(f"⏳ 等待换装任务完成...")
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/check-task-status"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    max_attempts = TIMEOUT_CONFIG["max_retry_attempts"]
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"🔄 第{attempt}次查询...")
        
        params = {"taskId": task_id}
        try:
            response = requests.get(url, headers=headers, params=params, 
                                  timeout=TIMEOUT_CONFIG["request_timeout"])
        except Exception as e:
            print(f"❌ 网络请求失败: {e}")
            time.sleep(TIMEOUT_CONFIG["task_check_interval"])
            continue
        
        if response.status_code == 200:
            try:
                result = response.json()
                status = result.get('data', 'UNKNOWN')
                
                print(f"📈 任务状态: {status}")
                
                if status == 'SUCCESS' and 'output' in result:
                    output = result['output']
                    result_url = output.get('resultUrl', '')
                    
                    print(f"🎉 换装任务完成！")
                    print(f"🔗 结果图URL: {result_url}")
                    
                    return download_image(result_url, "step1_fashion_result.png")
                    
                elif status in ['RUNNING', 'QUEUED', 'SUBMITTING']:
                    print(f"⏳ 任务进行中... 等待{TIMEOUT_CONFIG['task_check_interval']}秒后再次查询...")
                    time.sleep(TIMEOUT_CONFIG["task_check_interval"])
                    
                else:
                    print(f"❌ 任务失败，状态: {status}")
                    return None
                    
            except Exception as e:
                print(f"❌ 解析响应失败: {e}")
                time.sleep(TIMEOUT_CONFIG["task_check_interval"])
                continue
        else:
            print(f"❌ 查询失败: {response.status_code}")
            time.sleep(TIMEOUT_CONFIG["task_check_interval"])
            continue
    
    print("⏰ 等待超时")
    return None

def step2_remove_background(image_path):
    """步骤2: Clipdrop Remove-background 移除背景"""
    print("\n" + "="*60)
    print("🎯 步骤2: Clipdrop Remove-background 移除背景")
    print("="*60)
    
    if not image_path or not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return None
    
    print(f"📸 输入图片: {image_path}")
    
    url = f"{BASE_URL}/clipdrop/remove-background/v1"
    headers = {"x-api-key": API_KEY}
    
    try:
        with open(image_path, 'rb') as image_file:
            files = {
                'image_file': (os.path.basename(image_path), image_file, 'image/png')
            }
            
            print("📤 发送背景移除请求...")
            response = requests.post(url, headers=headers, files=files,
                                   timeout=TIMEOUT_CONFIG["request_timeout"])
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None
    
    print(f"📊 响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        output_path = os.path.join(OUTPUT_DIRS["temp"], "step2_no_background.png")
        
        with open(output_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ 背景移除完成！已保存: {output_path}")
        return output_path
    else:
        print(f"❌ 背景移除失败: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        return None

def step3_add_white_background(subject_image_path):
    """步骤3: 本地PIL添加白底背景"""
    print("\n" + "="*60)
    print("🎯 步骤3: 本地PIL添加白底背景")
    print("="*60)
    
    if not subject_image_path or not os.path.exists(subject_image_path):
        print(f"❌ 主体图片不存在: {subject_image_path}")
        return None
    
    try:
        # 打开主体图片（已移除背景的PNG）
        subject = Image.open(subject_image_path).convert("RGBA")
        width, height = subject.size
        print(f"📏 图片尺寸: {width}x{height}")
        
        # 创建白色背景
        background = Image.new('RGB', (width, height), WHITE_BACKGROUND_CONFIG["background_color"])
        
        # 将主体图片合成到白色背景上
        background.paste(subject, (0, 0), subject)
        
        # 保存结果
        output_path = os.path.join(OUTPUT_DIRS["results"], "final_white_background.png")
        background.save(output_path, WHITE_BACKGROUND_CONFIG["output_format"], 
                       dpi=(IMAGE_QUALITY["dpi"], IMAGE_QUALITY["dpi"]))
        
        print(f"✅ 白底背景合成完成！已保存: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"❌ 图片处理错误: {e}")
        return None

def download_image(url, filename):
    """下载图片"""
    if not url:
        print("❌ URL为空，无法下载")
        return None
    
    try:
        print(f"📥 下载图片: {filename}")
        response = requests.get(url, timeout=TIMEOUT_CONFIG["request_timeout"])
        if response.status_code == 200:
            filepath = os.path.join(OUTPUT_DIRS["temp"], filename)
            
            with open(filepath, 'wb') as f:
                f.write(response.content)
            print(f"✅ 图片已保存: {filepath}")
            return filepath
        else:
            print(f"❌ 下载失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 下载错误: {e}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='单张照片时尚换装工具')
    parser.add_argument('--model_image', type=str, help='模特图片路径')
    parser.add_argument('--clothes_image', type=str, help='服装图片路径')
    
    args = parser.parse_args()
    
    # 验证配置
    try:
        validate_config()
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        return
    
    # 创建输出目录
    create_output_dirs()
    
    # 获取输入文件路径
    if args.model_image and args.clothes_image:
        model_image = args.model_image
        clothes_image = args.clothes_image
    else:
        print("请输入文件路径:")
        model_image = input("模特图片路径: ").strip()
        clothes_image = input("服装图片路径: ").strip()
    
    print("=" * 80)
    print("🎯 单张照片时尚换装工具")
    print("=" * 80)
    print("📝 工作流程:")
    print("   1. 302.AI-ComfyUI 换装")
    print("   2. Clipdrop Remove-background 移除背景")
    print("   3. 本地PIL 添加白底背景")
    print()
    print(f"💰 预估成本: {get_total_cost_per_image()} PTC (约{get_total_cost_cny_per_image():.1f}元)")
    print()
    
    start_time = time.time()
    
    # 步骤1: 换装
    step1_result = step1_fashion_tryon(model_image, clothes_image)
    if not step1_result:
        print("❌ 步骤1失败，退出")
        return
    
    # 步骤2: 移除背景
    step2_result = step2_remove_background(step1_result)
    if not step2_result:
        print("❌ 步骤2失败，退出")
        return
    
    # 步骤3: 添加白底背景
    step3_result = step3_add_white_background(step2_result)
    if not step3_result:
        print("❌ 步骤3失败，退出")
        return
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print("\n" + "=" * 80)
    print("🎉 时尚换装完成！")
    print("=" * 80)
    print(f"⏱️  总耗时: {total_time:.1f}秒")
    print(f"💰 实际成本: {get_total_cost_per_image()} PTC (约{get_total_cost_cny_per_image():.1f}元)")
    print(f"📁 最终结果: {step3_result}")
    print("=" * 80)

if __name__ == "__main__":
    main()
