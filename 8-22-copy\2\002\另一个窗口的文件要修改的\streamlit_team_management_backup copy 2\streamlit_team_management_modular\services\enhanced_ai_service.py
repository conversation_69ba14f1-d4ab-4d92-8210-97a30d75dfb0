"""
增强的AI服务
集成OpenAI结构化输出和函数调用功能
适配用户隔离机制，与现有系统兼容
"""

from typing import List, Dict, Any, Optional, Tuple
import streamlit as st
import json
import uuid
from datetime import datetime
from openai import OpenAI

from config.settings import app_settings
from config.ai_schemas import (
    TEAM_INFO_SCHEMA, 
    PLAYER_INFO_SCHEMA, 
    FUNCTION_DEFINITIONS,
    STRUCTURED_OUTPUT_FORMAT,
    AI_CONFIG
)
from services.team_service import TeamService
from services.player_service import PlayerService
from services.enhanced_data_manager import EnhancedDataManager


class EnhancedAIService:
    """增强的AI服务，支持结构化输出和函数调用"""
    
    def __init__(self, user_id: str = None):
        """
        初始化增强AI服务
        
        Args:
            user_id: 用户ID，确保用户隔离
        """
        self.user_id = user_id or st.session_state.get('user_id', '')
        self.client = self._initialize_client()
        self.team_service = TeamService()
        self.player_service = PlayerService()
        self.enhanced_data_manager = EnhancedDataManager(self.user_id)
        
        # 配置
        self.model = AI_CONFIG["model"]
        self.enable_structured_outputs = AI_CONFIG["enable_structured_outputs"]
        self.enable_function_calling = AI_CONFIG["enable_function_calling"]

    def is_available(self) -> bool:
        """检查AI服务是否可用"""
        return self.client is not None
        
    def _initialize_client(self) -> OpenAI:
        """初始化OpenAI客户端"""
        try:
            api_key = st.secrets.get("OPENAI_API_KEY")
            if not api_key:
                st.error("❌ 未找到OpenAI API密钥，请在Streamlit secrets中配置OPENAI_API_KEY")
                return None
            return OpenAI(api_key=api_key)
        except Exception as e:
            st.error(f"❌ 初始化OpenAI客户端失败: {e}")
            return None
    
    def generate_response_with_functions(
        self,
        messages: List[Dict[str, str]],
        use_structured_output: bool = True  # 重新启用结构化输出
    ) -> Tuple[str, Optional[Dict[str, Any]]]:
        """
        生成带函数调用的AI响应
        
        Args:
            messages: 对话历史
            use_structured_output: 是否使用结构化输出
            
        Returns:
            Tuple[str, Optional[Dict]]: (AI回复, 函数调用结果)
        """
        if not self.client:
            return "❌ AI服务不可用", None
            
        try:
            # 准备API调用参数
            api_params = {
                "model": self.model,
                "messages": messages,
                "temperature": AI_CONFIG["temperature"],
                "max_tokens": AI_CONFIG["max_tokens"]
            }
            
            # 添加函数调用支持
            if self.enable_function_calling:
                api_params["tools"] = FUNCTION_DEFINITIONS
                api_params["tool_choice"] = "auto"
            
            # 添加结构化输出支持
            if use_structured_output and self.enable_structured_outputs:
                api_params["response_format"] = STRUCTURED_OUTPUT_FORMAT
            
            # 调用OpenAI API (兼容原始系统的函数调用逻辑)
            response = self.client.chat.completions.create(**api_params)
            
            # 处理响应
            message = response.choices[0].message
            ai_response = message.content or ""
            
            # 处理函数调用
            function_results = None
            if message.tool_calls:
                function_results = self._handle_function_calls(message.tool_calls)
                
                # 如果有函数调用，需要继续对话获取最终响应
                messages.append({
                    "role": "assistant", 
                    "content": ai_response,
                    "tool_calls": [tool_call.model_dump() for tool_call in message.tool_calls]
                })
                
                # 添加函数调用结果
                for tool_call, result in zip(message.tool_calls, function_results):
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": json.dumps(result, ensure_ascii=False)
                    })
                
                # 获取最终响应
                final_response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=AI_CONFIG["temperature"]
                )
                
                ai_response = final_response.choices[0].message.content or ai_response
            
            return ai_response, function_results
            
        except Exception as e:
            st.error(f"❌ AI响应生成失败: {e}")
            return f"抱歉，AI服务暂时不可用: {e}", None
    
    def _handle_function_calls(self, tool_calls) -> List[Dict[str, Any]]:
        """处理函数调用"""
        results = []
        
        for tool_call in tool_calls:
            function_name = tool_call.function.name
            try:
                arguments = json.loads(tool_call.function.arguments)
                result = self._execute_function(function_name, arguments)
                results.append(result)
            except Exception as e:
                results.append({"error": f"函数调用失败: {e}"})
        
        return results
    
    def _execute_function(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """执行具体的函数调用"""
        try:
            # 原有的提取函数
            if function_name == "extract_team_info":
                return self._extract_team_info(arguments)
            elif function_name == "extract_player_info":
                return self._extract_player_info(arguments)
            elif function_name == "validate_team_data":
                return self._validate_team_data(arguments)
            elif function_name == "generate_team_suggestions":
                return self._generate_team_suggestions(arguments)

            # 原始系统的数据管理函数
            elif function_name == "save_team_info":
                return self._save_team_info(arguments)
            elif function_name == "save_player_info":
                return self._save_player_info(arguments)
            elif function_name == "get_team_info":
                return self._get_team_info(arguments)
            elif function_name == "get_player_list":
                return self._get_player_list(arguments)
            elif function_name == "check_data_completeness":
                return self._check_data_completeness(arguments)
            elif function_name == "validate_jersey_number":
                return self._validate_jersey_number(arguments)

            # 高级功能函数
            elif function_name == "generate_team_logo":
                return self._generate_team_logo(arguments)
            elif function_name == "extract_team_info_from_text":
                return self._extract_team_info_from_text(arguments)

            else:
                return {"error": f"未知的函数: {function_name}"}
        except Exception as e:
            return {"error": f"函数执行失败: {e}"}
    
    def _extract_team_info(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """提取球队信息"""
        extracted_info = arguments.get("extracted_info", {})
        confidence = arguments.get("confidence", 0.0)
        missing_fields = arguments.get("missing_fields", [])

        # 验证提取的信息
        if not extracted_info:
            return {"error": "未提取到有效的球队信息"}

        # 检查必填字段
        basic_info = extracted_info.get("basic_info", {})
        if not basic_info.get("team_name"):
            return {"error": "缺少球队名称"}

        # 保存提取的信息到session state
        try:
            import streamlit as st
            if "ai_extracted_team_info" not in st.session_state:
                st.session_state.ai_extracted_team_info = {}

            # 合并新提取的信息
            st.session_state.ai_extracted_team_info.update(extracted_info)

            return {
                "success": True,
                "extracted_info": extracted_info,
                "confidence": confidence,
                "missing_fields": missing_fields,
                "message": f"球队信息提取成功，置信度: {confidence:.2%}"
            }
        except Exception as e:
            return {"error": f"保存球队信息失败: {e}"}
    
    def _extract_player_info(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """提取球员信息"""
        players = arguments.get("players", [])
        confidence = arguments.get("confidence", 0.0)
        validation_errors = arguments.get("validation_errors", [])

        if not players:
            return {"error": "未提取到有效的球员信息"}

        # 验证球员信息
        valid_players = []
        errors = []

        for i, player in enumerate(players, 1):
            basic_info = player.get("basic_info", {})
            if not basic_info.get("name"):
                errors.append(f"球员{i}缺少姓名")
                continue
            if not basic_info.get("jersey_number"):
                errors.append(f"球员{i}({basic_info['name']})缺少球衣号码")
                continue
            valid_players.append(player)

        # 保存到session state
        try:
            import streamlit as st
            if "ai_extracted_players" not in st.session_state:
                st.session_state.ai_extracted_players = []

            st.session_state.ai_extracted_players.extend(valid_players)

            return {
                "success": True,
                "players_count": len(valid_players),
                "players": valid_players,
                "confidence": confidence,
                "validation_errors": errors,
                "message": f"成功提取{len(valid_players)}名球员信息"
            }
        except Exception as e:
            return {"error": f"保存球员信息失败: {e}"}
    
    def _validate_team_data(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """验证球队数据"""
        team_name = arguments.get("team_name", "")
        
        if not team_name:
            return {"error": "球队名称不能为空"}
        
        # 获取球队统计信息
        try:
            stats = self.team_service.get_team_stats(team_name)
            return {
                "success": True,
                "team_name": team_name,
                "stats": stats,
                "message": "球队数据验证完成"
            }
        except Exception as e:
            return {"error": f"验证失败: {e}"}
    
    def _generate_team_suggestions(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """生成球队建议"""
        team_name = arguments.get("team_name", "")
        focus_area = arguments.get("focus_area", "球员管理")
        
        try:
            stats = self.team_service.get_team_stats(team_name)
            suggestions = []
            
            if stats["total_players"] < 5:
                suggestions.append("建议至少添加5名球员以满足比赛要求")
            
            if stats["players_with_photos"] < stats["total_players"]:
                suggestions.append("建议为所有球员上传照片")
            
            if stats["completion_rate"] < 80:
                suggestions.append("建议完善球员信息以提高完成度")
            
            return {
                "success": True,
                "team_name": team_name,
                "focus_area": focus_area,
                "suggestions": suggestions,
                "stats": stats
            }
        except Exception as e:
            return {"error": f"生成建议失败: {e}"}

    # 原始系统的数据管理函数实现
    def _save_team_info(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """保存球队信息"""
        try:
            team_data = arguments.get("team_data", {})
            team_id = arguments.get("team_id")

            if not team_data:
                return {"error": "缺少球队数据"}

            result = self.enhanced_data_manager.save_team_info(team_data, team_id)

            # 如果成功，更新当前上下文
            if result.get("success") and "current_team_id" not in st.session_state:
                st.session_state.current_team_id = result["team_id"]

            return result

        except Exception as e:
            return {"error": f"保存球队信息失败: {e}"}

    def _save_player_info(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """保存球员信息"""
        try:
            player_data = arguments.get("player_data", {})
            team_id = arguments.get("team_id") or st.session_state.get("current_team_id")

            if not player_data:
                return {"error": "缺少球员数据"}

            if not team_id:
                return {"error": "缺少球队ID，请先创建或选择球队"}

            return self.enhanced_data_manager.save_player_info(player_data, team_id)

        except Exception as e:
            return {"error": f"保存球员信息失败: {e}"}

    def _get_team_info(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """获取球队信息"""
        try:
            team_id = arguments.get("team_id") or st.session_state.get("current_team_id")

            if not team_id:
                return {"error": "缺少球队ID"}

            return self.enhanced_data_manager.get_team_info(team_id)

        except Exception as e:
            return {"error": f"获取球队信息失败: {e}"}

    def _get_player_list(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """获取球员列表"""
        try:
            team_id = arguments.get("team_id") or st.session_state.get("current_team_id")

            if not team_id:
                return {"error": "缺少球队ID"}

            return self.enhanced_data_manager.get_player_list(team_id)

        except Exception as e:
            return {"error": f"获取球员列表失败: {e}"}

    def _check_data_completeness(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """检查数据完整性"""
        try:
            team_id = arguments.get("team_id") or st.session_state.get("current_team_id")

            if not team_id:
                return {"error": "缺少球队ID"}

            return self.enhanced_data_manager.check_data_completeness(team_id)

        except Exception as e:
            return {"error": f"检查数据完整性失败: {e}"}

    def _validate_jersey_number(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """验证球衣号码"""
        try:
            team_id = arguments.get("team_id") or st.session_state.get("current_team_id")
            jersey_number = arguments.get("jersey_number")

            if not team_id:
                return {"error": "缺少球队ID"}

            if jersey_number is None:
                return {"error": "缺少球衣号码"}

            return self.enhanced_data_manager.validate_jersey_number(team_id, jersey_number)

        except Exception as e:
            return {"error": f"验证球衣号码失败: {e}"}

    # 高级功能函数实现
    def _generate_team_logo(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """生成队徽描述"""
        try:
            team_name = arguments.get("team_name", "")
            team_style = arguments.get("team_style", "现代")
            color_preference = arguments.get("color_preference", "蓝色")

            if not team_name:
                return {"error": "缺少球队名称"}

            # 使用AI生成队徽描述
            prompt = f"""
            请为足球队"{team_name}"设计一个队徽描述。

            要求：
            - 风格：{team_style}
            - 颜色偏好：{color_preference}
            - 适合足球队使用
            - 简洁明了，易于识别
            - 体现团队精神

            请提供详细的设计描述，包括：
            1. 主要图案元素
            2. 颜色搭配
            3. 整体布局
            4. 寓意说明
            """

            messages = [
                {"role": "system", "content": "你是一个专业的队徽设计师，擅长为足球队设计有意义的队徽。"},
                {"role": "user", "content": prompt}
            ]

            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=messages,
                temperature=0.8
            )

            logo_description = response.choices[0].message.content

            return {
                "success": True,
                "team_name": team_name,
                "style": team_style,
                "color_preference": color_preference,
                "logo_description": logo_description,
                "message": "队徽描述生成成功"
            }

        except Exception as e:
            return {"error": f"生成队徽描述失败: {e}"}

    def _extract_team_info_from_text(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """从文本中智能提取球队信息"""
        try:
            user_text = arguments.get("user_text", "")

            if not user_text:
                return {"error": "缺少用户文本"}

            # 使用AI解析文本并提取信息
            extraction_prompt = f"""
            请从以下用户输入的文本中提取球队报名信息。请提取以下字段：

            基本信息：
            - 球队名称
            - 联系人
            - 联系电话
            - 领队姓名
            - 队医姓名

            管理信息：
            - 教练姓名
            - 教练电话

            比赛信息：
            - 比赛名称
            - 比赛时间
            - 比赛地点
            - 参赛组别

            球衣信息：
            - 球衣颜色
            - 球裤颜色
            - 球袜颜色
            - 守门员服装颜色

            用户文本：
            {user_text}

            请以JSON格式返回提取的信息，如果某些信息未提及，请留空。
            """

            messages = [
                {"role": "system", "content": "你是一个专业的信息提取助手，擅长从自然语言中提取结构化信息。"},
                {"role": "user", "content": extraction_prompt}
            ]

            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.3
            )

            extracted_text = response.choices[0].message.content

            # 尝试解析JSON
            try:
                import json
                extracted_info = json.loads(extracted_text)
            except:
                # 如果解析失败，返回原始文本
                extracted_info = {"raw_text": extracted_text}

            # 🎨 自动队徽生成逻辑
            auto_logo_result = self._auto_generate_logo_if_possible(extracted_info)
            if auto_logo_result:
                extracted_info["auto_generated_logo"] = auto_logo_result

            return {
                "success": True,
                "user_text": user_text,
                "extracted_info": extracted_info,
                "confidence": 0.8,  # 基础置信度
                "message": "文本信息提取成功" + ("，已自动生成队徽" if auto_logo_result else "")
            }

        except Exception as e:
            return {"error": f"文本信息提取失败: {e}"}

    def _auto_generate_logo_if_possible(self, extracted_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """检查是否有足够信息自动生成队徽，如果有则自动生成"""
        try:
            # 检查是否有必要的信息来生成队徽
            team_name = extracted_info.get("球队名称") or extracted_info.get("team_name", "")
            jersey_color = extracted_info.get("球衣颜色") or extracted_info.get("jersey_color", "")

            # 如果没有球队名称，不生成队徽
            if not team_name or team_name.strip() == "":
                return None

            # 如果没有球衣颜色，使用默认颜色
            if not jersey_color or jersey_color.strip() == "":
                jersey_color = "蓝色"

            print(f"🎨 自动生成队徽：球队={team_name}, 颜色={jersey_color}")

            # 自动生成队徽
            logo_arguments = {
                "team_name": team_name,
                "team_style": "现代",  # 默认风格
                "color_preference": jersey_color
            }

            logo_result = self._generate_team_logo(logo_arguments)

            if logo_result and logo_result.get("success"):
                print(f"✅ 队徽自动生成成功：{logo_result.get('message', '')}")
                return {
                    "generated": True,
                    "team_name": team_name,
                    "logo_description": logo_result.get("logo_description", ""),
                    "auto_trigger": "team_info_extraction"
                }
            else:
                print(f"❌ 队徽自动生成失败：{logo_result.get('error', '未知错误')}")
                return None

        except Exception as e:
            print(f"❌ 自动生成队徽异常: {e}")
            return None

    def extract_info_from_text(self, text: str, info_type: str = "team") -> Dict[str, Any]:
        """从文本中提取信息（使用结构化输出）"""
        if info_type == "team":
            schema = TEAM_INFO_SCHEMA
            function_name = "extract_team_info"
        else:
            schema = PLAYER_INFO_SCHEMA
            function_name = "extract_player_info"
        
        messages = [
            {
                "role": "system",
                "content": f"你是一个专业的信息提取助手。请从用户提供的文本中提取{info_type}信息，并使用{function_name}函数返回结构化数据。"
            },
            {
                "role": "user",
                "content": f"请从以下文本中提取信息：\n\n{text}"
            }
        ]
        
        response, function_results = self.generate_response_with_functions(messages, use_structured_output=True)
        
        return {
            "response": response,
            "extracted_data": function_results[0] if function_results else None
        }


# 创建全局实例
enhanced_ai_service = EnhancedAIService()
