#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球队数据模型
Team Data Model

定义球队相关的数据结构
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Dict, Any, Optional
from models.player import Player


@dataclass
class TeamInfo:
    """球队基本信息"""
    name: str
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    display_name: Optional[str] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.display_name is None:
            self.display_name = '默认球队' if self.name == 'default' else self.name
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'created_at': self.created_at,
            'display_name': self.display_name
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TeamInfo':
        """从字典创建实例"""
        return cls(
            name=data.get('name', 'default'),
            created_at=data.get('created_at', datetime.now().isoformat()),
            display_name=data.get('display_name')
        )


@dataclass
class Team:
    """球队完整数据模型"""
    team_info: TeamInfo
    players: List[Player] = field(default_factory=list)
    
    @property
    def name(self) -> str:
        """球队名称"""
        return self.team_info.name
    
    @property
    def display_name(self) -> str:
        """球队显示名称"""
        return self.team_info.display_name
    
    @property
    def total_players(self) -> int:
        """球员总数"""
        return len(self.players)
    
    @property
    def players_with_photos(self) -> int:
        """有照片的球员数量"""
        return len([p for p in self.players if p.photo])
    
    def get_player_by_id(self, player_id: str) -> Optional[Player]:
        """根据ID获取球员"""
        for player in self.players:
            if player.id == player_id:
                return player
        return None
    
    def get_player_by_jersey_number(self, jersey_number: str) -> Optional[Player]:
        """根据球衣号码获取球员"""
        for player in self.players:
            if player.jersey_number == jersey_number:
                return player
        return None
    
    def add_player(self, player: Player) -> bool:
        """添加球员"""
        # 检查球衣号码是否已存在
        if self.get_player_by_jersey_number(player.jersey_number):
            return False
        
        self.players.append(player)
        return True
    
    def remove_player(self, player_id: str) -> bool:
        """移除球员"""
        for i, player in enumerate(self.players):
            if player.id == player_id:
                self.players.pop(i)
                return True
        return False
    
    def get_used_jersey_numbers(self) -> List[str]:
        """获取已使用的球衣号码"""
        return [player.jersey_number for player in self.players]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'team_info': self.team_info.to_dict(),
            'players': [player.to_dict() for player in self.players]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Team':
        """从字典创建实例"""
        team_info_data = data.get('team_info', {})
        team_info = TeamInfo.from_dict(team_info_data)
        
        players_data = data.get('players', [])
        players = [Player.from_dict(player_data) for player_data in players_data]
        
        return cls(team_info=team_info, players=players)
    
    @classmethod
    def create_empty(cls, team_name: str) -> 'Team':
        """创建空球队"""
        team_info = TeamInfo(name=team_name)
        return cls(team_info=team_info, players=[])
