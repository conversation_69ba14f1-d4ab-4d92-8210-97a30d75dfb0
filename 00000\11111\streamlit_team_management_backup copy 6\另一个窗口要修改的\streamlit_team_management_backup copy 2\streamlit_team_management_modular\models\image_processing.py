#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片处理数据模型
Image Processing Data Model

定义独立AI修图功能的数据结构
"""

import uuid
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, Optional, List
from enum import Enum


class ProcessingType(Enum):
    """处理类型枚举"""
    FASHION_TRYON = "fashion_tryon"  # 换装
    REMOVE_BACKGROUND = "remove_background"  # 背景去除
    ADD_WHITE_BACKGROUND = "add_white_background"  # 添加白底
    COMBINED = "combined"  # 组合处理


@dataclass
class ImageProcessingRequest:
    """图片处理请求"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    source_image_path: str = ""
    template_image_path: Optional[str] = None
    processing_types: List[ProcessingType] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'source_image_path': self.source_image_path,
            'template_image_path': self.template_image_path,
            'processing_types': [pt.value for pt in self.processing_types],
            'parameters': self.parameters,
            'created_at': self.created_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ImageProcessingRequest':
        """从字典创建实例"""
        processing_types = [ProcessingType(pt) for pt in data.get('processing_types', [])]
        return cls(
            id=data.get('id', str(uuid.uuid4())),
            source_image_path=data.get('source_image_path', ''),
            template_image_path=data.get('template_image_path'),
            processing_types=processing_types,
            parameters=data.get('parameters', {}),
            created_at=data.get('created_at', datetime.now().isoformat())
        )


@dataclass
class ProcessingResult:
    """处理结果"""
    request_id: str
    success: bool
    processed_image_path: Optional[str] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'request_id': self.request_id,
            'success': self.success,
            'processed_image_path': self.processed_image_path,
            'error_message': self.error_message,
            'processing_time': self.processing_time,
            'created_at': self.created_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProcessingResult':
        """从字典创建实例"""
        return cls(
            request_id=data.get('request_id', ''),
            success=data.get('success', False),
            processed_image_path=data.get('processed_image_path'),
            error_message=data.get('error_message'),
            processing_time=data.get('processing_time', 0.0),
            created_at=data.get('created_at', datetime.now().isoformat())
        )


@dataclass
class ProcessingSession:
    """处理会话"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    requests: List[ImageProcessingRequest] = field(default_factory=list)
    results: List[ProcessingResult] = field(default_factory=list)
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def add_request(self, request: ImageProcessingRequest) -> None:
        """添加处理请求"""
        self.requests.append(request)
    
    def add_result(self, result: ProcessingResult) -> None:
        """添加处理结果"""
        self.results.append(result)
    
    def get_latest_result(self) -> Optional[ProcessingResult]:
        """获取最新结果"""
        return self.results[-1] if self.results else None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'requests': [req.to_dict() for req in self.requests],
            'results': [res.to_dict() for res in self.results],
            'created_at': self.created_at
        }
