#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试核心自动Word生成功能
Test Core Auto Word Generation

验证AI换装后自动触发Word生成的核心功能
"""

import os
import sys
import tempfile
from PIL import Image

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

def create_test_image(width=400, height=600, color=(255, 0, 0)):
    """创建测试图片"""
    img = Image.new('RGB', (width, height), color)
    return img

def test_core_auto_word_generation():
    """测试核心自动Word生成功能"""
    print("🧪 测试核心自动Word生成功能...")
    
    try:
        from services.ai_image_engine import AIImageEngine
        from models.image_processing import ImageProcessingRequest, ProcessingType
        
        # 创建AI图像引擎
        engine = AIImageEngine()
        print("✅ AI图像引擎创建成功")
        
        # 创建测试图片
        test_img = create_test_image()
        temp_img_path = os.path.join(tempfile.gettempdir(), "test_core_player.jpg")
        test_img.save(temp_img_path, 'JPEG')
        print(f"✅ 测试图片创建成功: {temp_img_path}")
        
        # 准备球队上下文（包含完整信息）
        team_context = {
            'team_name': '核心测试足球队',
            'organization': '测试组织',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'current_player': {
                'name': '核心测试球员',
                'number': '10',
                'photo_path': temp_img_path
            },
            'other_players': [
                {
                    'name': '球员2',
                    'number': '9',
                    'photo_path': ''
                }
            ]
        }
        
        # 创建处理请求（包含球队上下文和换装处理）
        request = ImageProcessingRequest(
            source_image_path=temp_img_path,
            processing_types=[ProcessingType.FASHION_TRYON, ProcessingType.ADD_WHITE_BACKGROUND],
            team_context=team_context
        )
        
        print("✅ 处理请求创建成功")
        print(f"   球队名称: {team_context['team_name']}")
        print(f"   当前球员: {team_context['current_player']['name']}")
        print(f"   处理类型: 换装 + 白底")
        
        # 执行处理（这应该触发自动Word生成）
        print("🎨 开始AI图像处理（包含自动Word生成）...")
        result = engine.process_image(request)
        
        if result.success:
            print("✅ AI图像处理成功!")
            print(f"   处理后图片: {result.processed_image_path}")
            print(f"   处理时间: {result.processing_time:.2f}秒")
            
            # 检查是否生成了Word文件
            word_output_dir = "word_output"
            if os.path.exists(word_output_dir):
                word_files = [f for f in os.listdir(word_output_dir) if f.endswith('.docx')]
                if word_files:
                    print(f"✅ 发现Word文件: {len(word_files)} 个")
                    for word_file in word_files[-3:]:  # 显示最新的3个文件
                        file_path = os.path.join(word_output_dir, word_file)
                        file_size = os.path.getsize(file_path)
                        print(f"   📄 {word_file} ({file_size} 字节)")
                else:
                    print("⚠️ 未发现Word文件")
            else:
                print("⚠️ Word输出目录不存在")
            
            return True
        else:
            print(f"❌ AI图像处理失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            if 'temp_img_path' in locals() and os.path.exists(temp_img_path):
                os.remove(temp_img_path)
        except:
            pass

def test_trigger_logic():
    """测试自动触发逻辑"""
    print("\n🧪 测试自动触发逻辑...")
    
    try:
        from services.ai_image_engine import AIImageEngine
        from models.image_processing import ImageProcessingRequest, ProcessingType
        
        engine = AIImageEngine()
        
        # 测试1: 没有换装处理，不应该触发Word生成
        print("📝 测试1: 没有换装处理")
        request1 = ImageProcessingRequest(
            source_image_path="dummy_path.jpg",
            processing_types=[ProcessingType.ADD_WHITE_BACKGROUND],
            team_context={'team_name': '测试队'}
        )
        
        # 模拟检查触发条件
        has_fashion_tryon = ProcessingType.FASHION_TRYON in request1.processing_types
        print(f"   包含换装处理: {has_fashion_tryon}")
        print(f"   应该触发Word生成: {has_fashion_tryon}")
        
        # 测试2: 有换装处理但没有球队上下文
        print("\n📝 测试2: 有换装处理但没有球队上下文")
        request2 = ImageProcessingRequest(
            source_image_path="dummy_path.jpg",
            processing_types=[ProcessingType.FASHION_TRYON],
            team_context=None
        )
        
        has_fashion_tryon = ProcessingType.FASHION_TRYON in request2.processing_types
        has_team_context = request2.team_context is not None
        should_trigger = has_fashion_tryon and has_team_context
        print(f"   包含换装处理: {has_fashion_tryon}")
        print(f"   有球队上下文: {has_team_context}")
        print(f"   应该触发Word生成: {should_trigger}")
        
        # 测试3: 有换装处理且有球队上下文
        print("\n📝 测试3: 有换装处理且有球队上下文")
        request3 = ImageProcessingRequest(
            source_image_path="dummy_path.jpg",
            processing_types=[ProcessingType.FASHION_TRYON],
            team_context={'team_name': '测试队', 'current_player': {'name': '球员'}}
        )
        
        has_fashion_tryon = ProcessingType.FASHION_TRYON in request3.processing_types
        has_team_context = request3.team_context is not None
        should_trigger = has_fashion_tryon and has_team_context
        print(f"   包含换装处理: {has_fashion_tryon}")
        print(f"   有球队上下文: {has_team_context}")
        print(f"   应该触发Word生成: {should_trigger}")
        
        print("✅ 触发逻辑测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 触发逻辑测试异常: {e}")
        return False

def check_java_word_generator():
    """检查Java Word生成器"""
    print("\n🧪 检查Java Word生成器...")
    
    try:
        # 检查JAR文件
        jar_path = "../word_zc/ai-football-generator/target/word-generator.jar"
        if os.path.exists(jar_path):
            jar_size = os.path.getsize(jar_path)
            print(f"✅ JAR文件存在: {jar_path} ({jar_size} 字节)")
        else:
            print(f"❌ JAR文件不存在: {jar_path}")
            return False
        
        # 检查模板文件
        template_path = "../word_zc/ai-football-generator/template.docx"
        if os.path.exists(template_path):
            template_size = os.path.getsize(template_path)
            print(f"✅ 模板文件存在: {template_path} ({template_size} 字节)")
        else:
            print(f"❌ 模板文件不存在: {template_path}")
            return False
        
        # 检查输出目录
        output_dir = "word_output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"✅ 创建输出目录: {output_dir}")
        else:
            print(f"✅ 输出目录存在: {output_dir}")
        
        print("✅ Java Word生成器环境检查完成")
        return True
        
    except Exception as e:
        print(f"❌ Java Word生成器检查异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试核心自动Word生成功能")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("Java Word生成器环境检查", check_java_word_generator),
        ("自动触发逻辑测试", test_trigger_logic),
        ("核心自动Word生成测试", test_core_auto_word_generation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 核心自动Word生成功能测试结果")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 核心功能测试通过！")
        print("\n💡 实现的自动化流程:")
        print("   1. ✅ AI换装处理完成")
        print("   2. ✅ 自动检测换装类型和球队上下文")
        print("   3. ✅ 自动触发Word生成")
        print("   4. ✅ Java自动裁剪照片为正方形")
        print("   5. ✅ 生成包含裁剪照片的Word报名表")
        
        print("\n🎯 关键特性:")
        print("   ✅ 智能触发 - 只有换装处理才触发Word生成")
        print("   ✅ 上下文感知 - 需要球队信息才能生成")
        print("   ✅ 自动化流程 - 无需用户手动操作")
        print("   ✅ 复用Java逻辑 - 保持原有的裁剪和Word生成")
    else:
        print(f"\n⚠️ 还有 {total-passed} 个问题需要进一步检查")
        print("\n💡 可能的问题:")
        print("   - Java环境配置")
        print("   - 文件路径问题")
        print("   - 依赖模块缺失")
    
    return passed == total

if __name__ == "__main__":
    main()
