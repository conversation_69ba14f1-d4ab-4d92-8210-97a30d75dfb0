# 🚀 快速开始指南 (Quick Start Guide)

欢迎使用时尚换装工具包！本指南将帮助您在5分钟内开始使用。

## 📋 准备工作

### 1. 系统要求
- Python 3.7 或更高版本
- 稳定的网络连接
- 302.AI API密钥

### 2. 获取API密钥
1. 访问 [302.AI](https://302.ai)
2. 注册账户并获取API密钥
3. 确保账户有足够的PTC余额

## ⚡ 5分钟快速安装

### 步骤1: 下载工具包
```bash
# 下载并解压工具包到本地目录
# 确保所有文件都在同一文件夹中
```

### 步骤2: 安装依赖
```bash
# 方法1: 使用安装脚本 (推荐)
python setup.py

# 方法2: 手动安装
pip install -r requirements.txt
```

### 步骤3: 配置API密钥
编辑 `config.py` 文件，替换API密钥：
```python
# 找到这一行
API_KEY = "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o"

# 替换为您的实际密钥
API_KEY = "sk-your-actual-api-key-here"
```

### 步骤4: 测试安装
```bash
python test_toolkit.py
```

如果看到 "🎉 所有测试通过！" 说明安装成功！

## 🎯 立即开始使用

### 单张照片处理

```bash
# 基本用法
python single_fashion_tryon.py --model_image model.jpg --clothes_image clothes.png

# 交互式使用
python single_fashion_tryon.py
# 然后按提示输入文件路径
```

### 批量照片处理

```bash
# 批量处理文件夹中的所有照片
python batch_fashion_tryon.py --input_folder photos/ --clothes_image clothes.png

# 交互式使用
python batch_fashion_tryon.py
# 然后按提示输入文件夹路径
```

## 📁 文件准备

### 模特照片要求
- ✅ 格式: JPG, PNG, BMP, TIFF
- ✅ 人物清晰可见
- ✅ 服装部位明确
- ✅ 建议尺寸: 512x640 或更大

### 服装图片要求
- ✅ 格式: PNG (推荐), JPG
- ✅ 服装清晰完整
- ✅ 最好是纯色背景
- ✅ 建议尺寸: 512x640 或更大

## 💰 成本说明

| 步骤 | API | 成本 |
|------|-----|------|
| 换装 | 302.AI-ComfyUI | 0.1 PTC |
| 背景移除 | Clipdrop | 0.5 PTC |
| 白底合成 | 本地处理 | 免费 |
| **总计** | - | **0.6 PTC/张 (约4.2元)** |

## 📊 处理流程

```
原始照片 → 换装 → 移除背景 → 添加白底 → 最终结果
   ↓         ↓        ↓         ↓         ↓
 model.jpg → step1 → step2 → step3 → final.png
```

## 🎉 成功示例

处理完成后，您会看到类似输出：

```
🎉 时尚换装完成！
================================================================================
⏱️  总耗时: 156.3秒
💰 实际成本: 0.6 PTC (约4.2元)
📁 最终结果: results/final_white_background.png
================================================================================
```

## 🔧 常见问题

### Q: API密钥错误怎么办？
A: 检查 `config.py` 中的 `API_KEY` 是否正确设置

### Q: 网络连接失败？
A: 确保网络连接正常，可以尝试增加超时时间

### Q: 图片格式不支持？
A: 使用 JPG 或 PNG 格式，避免使用 WEBP 等特殊格式

### Q: 处理时间太长？
A: 正常情况下单张照片需要2-5分钟，请耐心等待

## 📚 进阶使用

### 自定义配置
编辑 `config.py` 文件可以调整：
- 超时时间
- 输出目录
- 图片质量
- 成本设置

### 批量处理优化
- 准备好所有图片放在同一文件夹
- 使用相同的服装图片可以节省时间
- 定期检查处理进度

### 结果分析
处理完成后查看：
- `analysis_reports/batch_test_report.json` - 详细数据
- `batch_results/` - 最终结果图片
- `temp_files/` - 中间处理文件

## 🆘 获取帮助

如果遇到问题，请查看：

1. **详细文档**
   - `README.md` - 项目概述
   - `API_GUIDE.md` - API详细说明
   - `EXAMPLES.md` - 使用示例

2. **测试工具**
   ```bash
   python test_toolkit.py
   ```

3. **配置检查**
   ```bash
   python -c "from config import validate_config; validate_config()"
   ```

## 🎯 下一步

现在您已经可以开始使用时尚换装工具包了！

建议的学习路径：
1. ✅ 完成快速开始 (当前步骤)
2. 📖 阅读 `EXAMPLES.md` 学习更多用法
3. 🔧 根据需要调整 `config.py` 配置
4. 🚀 开始您的时尚换装项目！

---

**🎉 祝您使用愉快！如有问题，请参考详细文档或重新运行测试脚本。**
