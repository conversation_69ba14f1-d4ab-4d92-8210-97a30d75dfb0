#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word生成集成测试
测试Python通过Subprocess调用Java生成Word文档
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from word_generator_service import WordGeneratorService

def test_word_generation():
    """测试Word生成功能"""
    print("🧪 开始Word生成集成测试...")
    print("=" * 50)
    
    try:
        # 1. 初始化Word生成服务
        print("🔧 初始化Word生成服务...")
        
        jar_path = "../word_zc/ai-football-generator/target/word-generator.jar"
        template_path = "../word_zc/ai-football-generator/template.docx"
        output_dir = "word_output"
        
        word_service = WordGeneratorService(jar_path, template_path, output_dir)
        print("✅ Word生成服务初始化成功")
        
        # 2. 准备测试数据
        print("📝 准备测试数据...")
        
        team_data = {
            'name': 'Python测试球队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五'
        }
        
        players_data = [
            {
                'name': '球员1',
                'jersey_number': '10',
                'photo': '../word_zc/ai-football-generator/photos/player1.png'
            },
            {
                'name': '球员2', 
                'jersey_number': '9',
                'photo': '../word_zc/ai-football-generator/photos/player2.jpg'
            },
            {
                'name': '球员3',
                'jersey_number': '8',
                'photo': '../word_zc/ai-football-generator/photos/player3.jpg'
            }
        ]
        
        print(f"✅ 测试数据准备完成 - 球队: {team_data['name']}, 球员: {len(players_data)}人")
        
        # 3. 生成Word报名表
        print("📄 生成Word报名表...")
        
        result = word_service.generate_report(team_data, players_data)
        
        # 4. 检查结果
        if result['success']:
            print("✅ Word报名表生成成功！")
            print(f"📁 文件路径: {result['file_path']}")
            print(f"📊 球队名称: {result.get('team_name', 'N/A')}")
            print(f"👥 球员数量: {result.get('player_count', 'N/A')}")
            
            # 检查文件是否存在
            if os.path.exists(result['file_path']):
                file_size = os.path.getsize(result['file_path'])
                print(f"📏 文件大小: {file_size / 1024:.1f} KB")
                print("🎉 集成测试完全成功！")
                return True
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print("❌ Word报名表生成失败")
            print(f"错误信息: {result['message']}")
            if 'error' in result:
                print(f"错误详情: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_management():
    """测试文件管理功能"""
    print("\n🗂️ 测试文件管理功能...")
    
    try:
        jar_path = "../word_zc/ai-football-generator/target/word-generator.jar"
        template_path = "../word_zc/ai-football-generator/template.docx"
        output_dir = "word_output"
        
        word_service = WordGeneratorService(jar_path, template_path, output_dir)
        
        # 获取输出文件列表
        files = word_service.get_output_files()
        print(f"📋 找到 {len(files)} 个输出文件")
        
        for i, file_info in enumerate(files[:5], 1):  # 只显示前5个
            print(f"  {i}. {file_info['name']} ({file_info['size'] / 1024:.1f} KB)")
        
        if len(files) > 5:
            print(f"  ... 还有 {len(files) - 5} 个文件")
        
        print("✅ 文件管理功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 文件管理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Word生成集成测试套件")
    print("=" * 60)
    
    # 测试1: Word生成功能
    test1_success = test_word_generation()
    
    # 测试2: 文件管理功能
    test2_success = test_file_management()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   Word生成功能: {'✅ 成功' if test1_success else '❌ 失败'}")
    print(f"   文件管理功能: {'✅ 成功' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过！Subprocess集成完全成功！")
        print("💡 现在可以在Streamlit应用中使用Word生成功能了")
        print("\n📋 下一步:")
        print("   1. 启动Streamlit应用")
        print("   2. 在AI助手标签页中测试Word生成")
        print("   3. 验证完整的用户工作流程")
    else:
        print("\n⚠️ 部分测试失败，请检查配置和环境")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
