import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.Pictures;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * Word生成核心模块 - 纯净版
 * 专门用于生成足球报名表Word文档
 * 移除了AI对话、命令行界面等重复功能
 * 专为Python集成优化
 */
public class WordGeneratorCore {
    private String templatePath;
    private String outputDirectory;
    private String photosDirectory;
    
    /**
     * 构造函数
     * @param templatePath Word模板文件路径
     * @param outputDirectory 输出目录
     * @param photosDirectory 照片目录
     */
    public WordGeneratorCore(String templatePath, String outputDirectory, String photosDirectory) {
        this.templatePath = templatePath;
        this.outputDirectory = outputDirectory;
        this.photosDirectory = photosDirectory;
        
        // 确保输出目录存在
        createDirectoryIfNotExists(outputDirectory);
        createDirectoryIfNotExists(photosDirectory);
    }
    
    /**
     * 生成Word报名表 - 核心方法
     * @param teamData 球队数据
     * @return 生成的文件路径，失败返回null
     */
    public String generateReport(FootballTeamData teamData) {
        try {
            System.out.println("🚀 开始生成Word报名表...");
            
            // 验证数据
            if (!validateTeamData(teamData)) {
                System.err.println("❌ 球队数据验证失败");
                return null;
            }
            
            // 1. 准备模板数据
            Map<String, Object> templateData = prepareTemplateData(teamData);
            
            // 2. 生成输出文件名
            String outputPath = generateOutputPath(teamData);
            
            // 3. 使用模板生成文档
            try (FileInputStream templateStream = new FileInputStream(templatePath)) {
                XWPFTemplate template = XWPFTemplate.compile(templateStream).render(templateData);
                
                try (FileOutputStream outputStream = new FileOutputStream(outputPath)) {
                    template.write(outputStream);
                }
                
                template.close();
                
                System.out.println("✅ Word报名表生成成功：" + outputPath);
                return outputPath;
                
            }
            
        } catch (Exception e) {
            System.err.println("❌ 生成Word报名表失败：" + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 验证球队数据
     */
    private boolean validateTeamData(FootballTeamData teamData) {
        if (teamData == null) {
            System.err.println("球队数据为空");
            return false;
        }
        
        if (!teamData.isValid()) {
            System.err.println("球队数据无效");
            return false;
        }
        
        if (teamData.getPlayerCount() == 0) {
            System.err.println("没有有效的球员数据");
            return false;
        }
        
        System.out.println("✅ 数据验证通过：" + teamData.getValidPlayersInfo());
        return true;
    }
    
    /**
     * 准备模板数据
     */
    private Map<String, Object> prepareTemplateData(FootballTeamData teamData) {
        Map<String, Object> data = new HashMap<>();
        
        // 添加队伍基本信息
        TeamInfo teamInfo = teamData.getTeamInfo();
        if (teamInfo != null) {
            data.put("title", teamInfo.getTitle() != null ? teamInfo.getTitle() : "足球比赛报名表");
            data.put("organizationName", teamInfo.getOrganizationName() != null ? teamInfo.getOrganizationName() : "");
            data.put("teamLeader", teamInfo.getTeamLeader() != null ? teamInfo.getTeamLeader() : "");
            data.put("coach", teamInfo.getCoach() != null ? teamInfo.getCoach() : "");
            data.put("teamDoctor", teamInfo.getTeamDoctor() != null ? teamInfo.getTeamDoctor() : "");
        }
        
        // 添加球员信息
        PlayerData[] players = teamData.getPlayers();
        for (int i = 0; i < 10; i++) {
            int playerIndex = i + 1;
            
            if (i < players.length && players[i] != null && players[i].isValid()) {
                PlayerData player = players[i];
                addPlayerData(data, playerIndex,
                            player.getNumber() != null ? player.getNumber() : "",
                            player.getName() != null ? player.getName() : "",
                            player.getPhotoPath() != null ? player.getPhotoPath() : "");
            } else {
                // 空球员位置
                data.put("player" + playerIndex + "Number", "");
                data.put("player" + playerIndex + "Name", "");
            }
        }
        
        return data;
    }
    
    /**
     * 添加单个球员数据到模板
     */
    private void addPlayerData(Map<String, Object> data, int playerIndex,
                              String number, String name, String photoPath) {
        try {
            // 添加球员号码和姓名
            data.put("player" + playerIndex + "Number", number);
            data.put("player" + playerIndex + "Name", name);
            
            // 处理球员照片
            if (photoPath != null && !photoPath.trim().isEmpty()) {
                String processedPhotoPath = processPlayerPhoto(photoPath, playerIndex);
                if (processedPhotoPath != null) {
                    data.put("player" + playerIndex + "Photo",
                        Pictures.ofLocal(processedPhotoPath)
                            .size(100, 120)  // 设置图片大小：宽100px，高120px
                            .create());
                    
                    System.out.println("✅ 添加球员：" + number + "号 " + name + " (照片: " + processedPhotoPath + ")");
                } else {
                    System.out.println("✅ 添加球员：" + number + "号 " + name + " (照片处理失败)");
                }
            } else {
                System.out.println("✅ 添加球员：" + number + "号 " + name + " (无照片)");
            }
            
        } catch (Exception e) {
            System.err.println("⚠️ 处理球员数据失败 " + name + "：" + e.getMessage());
            // 如果处理失败，只添加文本信息
            data.put("player" + playerIndex + "Number", number);
            data.put("player" + playerIndex + "Name", name);
        }
    }
    
    /**
     * 处理球员照片（裁剪为正方形）
     */
    private String processPlayerPhoto(String imagePath, int playerIndex) {
        try {
            return cropImageToSquare(imagePath, playerIndex);
        } catch (Exception e) {
            System.err.println("⚠️ 照片处理失败 " + imagePath + "：" + e.getMessage());
            return null;
        }
    }
    
    /**
     * 生成输出文件路径
     */
    private String generateOutputPath(FootballTeamData teamData) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String teamName = "football_team";

        if (teamData.getTeamInfo() != null && teamData.getTeamInfo().getOrganizationName() != null) {
            // 保持原始团队名称，只替换文件系统不支持的字符
            teamName = teamData.getTeamInfo().getOrganizationName()
                .replaceAll("[<>:\"/\\\\|?*]", "_");  // 只替换Windows文件系统不支持的字符
        }

        return outputDirectory + "/" + teamName + "_registration_" + timestamp + ".docx";
    }
    
    /**
     * 将图片裁剪为正方形（居中裁剪）
     */
    private String cropImageToSquare(String imagePath, int playerIndex) throws Exception {
        File imageFile = new File(imagePath);
        if (!imageFile.exists()) {
            throw new IOException("图片文件不存在: " + imagePath);
        }
        
        BufferedImage originalImage = ImageIO.read(imageFile);
        if (originalImage == null) {
            throw new IOException("无法读取图片: " + imagePath);
        }
        
        int width = originalImage.getWidth();
        int height = originalImage.getHeight();
        
        // 计算正方形的边长（取较小的尺寸）
        int size = Math.min(width, height);
        
        // 计算裁剪的起始位置（居中）
        int x = (width - size) / 2;
        int y = (height - size) / 2;
        
        // 裁剪图片为正方形
        BufferedImage croppedImage = originalImage.getSubimage(x, y, size, size);
        
        // 根据原图格式确定输出格式和扩展名
        String originalExtension = getFileExtension(imagePath);
        String outputFormat = originalExtension.equalsIgnoreCase("jpg") || originalExtension.equalsIgnoreCase("jpeg") ? "JPG" : "PNG";
        String outputExtension = outputFormat.toLowerCase();
        
        // 保存裁剪后的图片
        String croppedPath = photosDirectory + "/player" + playerIndex + "_cropped." + outputExtension;
        ImageIO.write(croppedImage, outputFormat, new File(croppedPath));
        
        System.out.println("✂️ 图片已裁剪：" + imagePath + " -> " + croppedPath +
                          " (原尺寸: " + width + "x" + height + " -> 裁剪尺寸: " + size + "x" + size + ")");
        return croppedPath;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filePath) {
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex + 1);
        }
        return "";
    }
    
    /**
     * 创建目录（如果不存在）
     */
    private void createDirectoryIfNotExists(String directoryPath) {
        File directory = new File(directoryPath);
        if (!directory.exists()) {
            boolean created = directory.mkdirs();
            if (created) {
                System.out.println("📁 创建目录：" + directoryPath);
            }
        }
    }
    
    // Getters for configuration
    public String getTemplatePath() {
        return templatePath;
    }
    
    public String getOutputDirectory() {
        return outputDirectory;
    }
    
    public String getPhotosDirectory() {
        return photosDirectory;
    }
}
