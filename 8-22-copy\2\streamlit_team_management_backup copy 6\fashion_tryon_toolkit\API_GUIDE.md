# 📖 API使用指南 (API Usage Guide)

本文档详细说明了时尚换装工具包中使用的各个API的配置和使用方法。

## 🔑 API密钥配置

### 1. 获取API密钥

您需要从 [302.AI](https://302.ai) 获取API密钥。

### 2. 配置密钥

编辑 `config.py` 文件中的 `API_KEY` 变量：

```python
API_KEY = "your-actual-api-key-here"
```

⚠️ **重要**: 请不要将API密钥提交到版本控制系统中！

## 🎯 API详细说明

### 1. 302.AI-ComfyUI 换装API

#### 功能
- 将模特照片中的服装替换为指定的服装
- 支持上衣、裤子、裙子等不同类型的服装

#### 端点
```
POST /302/comfyui/clothes-changer/create-task
```

#### 请求参数
| 参数 | 类型 | 说明 |
|------|------|------|
| `modelImageFile` | File | 模特图片文件 |
| `clothesImageFile` | File | 服装图片文件 |
| `modelImgSegLabels` | String | 模特图片分割标签 (10-上衣, 5-裤子, 6-裙子) |
| `clothesImgSegLabels` | String | 服装图片分割标签 (10-上衣, 5-裤子, 6-裙子) |

#### 响应格式
```json
{
  "code": 200,
  "data": {
    "taskId": "task-uuid-here"
  }
}
```

#### 成本
- **0.1 PTC** 每次调用

#### 状态查询
```
GET /302/comfyui/clothes-changer/check-task-status?taskId={taskId}
```

可能的状态：
- `SUBMITTING`: 任务提交中
- `QUEUED`: 任务排队中
- `RUNNING`: 任务执行中
- `SUCCESS`: 任务成功完成
- `FAILED`: 任务失败

### 2. Clipdrop 背景移除API

#### 功能
- 精确移除图片背景
- 保留主体对象的完整性

#### 端点
```
POST /clipdrop/remove-background/v1
```

#### 请求参数
| 参数 | 类型 | 说明 |
|------|------|------|
| `image_file` | File | 需要移除背景的图片文件 |

#### 请求头
```
x-api-key: your-api-key
```

#### 响应
- 成功: 返回移除背景后的PNG图片数据
- 失败: 返回错误信息

#### 成本
- **0.5 PTC** 每次调用

### 3. 本地PIL白底合成

#### 功能
- 将透明背景的图片合成到白色背景上
- 本地处理，无需API调用

#### 实现
```python
from PIL import Image

# 打开透明背景图片
subject = Image.open(image_path).convert("RGBA")
width, height = subject.size

# 创建白色背景
background = Image.new('RGB', (width, height), 'white')

# 合成图片
background.paste(subject, (0, 0), subject)

# 保存结果
background.save(output_path, 'PNG')
```

#### 成本
- **免费** (本地处理)

## ⚙️ 配置参数说明

### 超时设置
```python
TIMEOUT_CONFIG = {
    "request_timeout": 30,        # 单次请求超时 (秒)
    "task_max_wait": 600,         # 任务最大等待时间 (秒)
    "task_check_interval": 30,    # 任务状态检查间隔 (秒)
    "max_retry_attempts": 20      # 最大重试次数
}
```

### 换装参数
```python
FASHION_TRYON_CONFIG = {
    "modelImgSegLabels": "10",    # 模特图片分割标签
    "clothesImgSegLabels": "10"   # 服装图片分割标签
}
```

#### 分割标签说明
- `10`: 上衣 (T恤、衬衫、外套等)
- `5`: 裤子 (长裤、短裤等)
- `6`: 裙子 (连衣裙、短裙等)

### 图片质量设置
```python
IMAGE_QUALITY = {
    "jpeg_quality": 95,           # JPEG质量 (1-100)
    "png_compress_level": 6,      # PNG压缩级别 (0-9)
    "dpi": 300                    # 图片DPI
}
```

## 🚨 错误处理

### 常见错误及解决方案

#### 1. API密钥错误
```
❌ 配置错误: 请设置正确的API_KEY
```
**解决方案**: 检查 `config.py` 中的 `API_KEY` 是否正确设置

#### 2. 网络超时
```
❌ 网络请求失败: timeout
```
**解决方案**: 
- 检查网络连接
- 增加 `request_timeout` 值
- 重试请求

#### 3. 任务失败
```
❌ 任务失败，状态: FAILED
```
**解决方案**:
- 检查输入图片格式和质量
- 确认图片中包含明确的人物和服装
- 重试任务

#### 4. 文件不存在
```
❌ 图片文件不存在: path/to/image.jpg
```
**解决方案**: 检查文件路径是否正确

## 📊 成本优化建议

### 1. 批量处理
- 使用批量处理工具可以更好地监控成本
- 批量处理时成本线性增长，无额外费用

### 2. 图片预处理
- 确保输入图片质量良好，避免重复处理
- 适当调整图片尺寸，减少处理时间

### 3. 错误处理
- 完善的错误处理可以避免不必要的API调用
- 保存中间结果，避免重复处理

## 🔧 高级配置

### 自定义输出目录
```python
OUTPUT_DIRS = {
    "temp": "my_temp_files",
    "results": "my_results",
    "batch_results": "my_batch_results",
    "analysis": "my_analysis"
}
```

### 自定义图片格式支持
```python
SUPPORTED_FORMATS = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"]
```

### 批量处理配置
```python
BATCH_CONFIG = {
    "max_concurrent": 1,          # 最大并发数
    "progress_report_interval": 1, # 进度报告间隔
    "auto_retry_failed": True,    # 自动重试失败任务
    "save_intermediate": True     # 保存中间结果
}
```

## 📈 性能监控

工具包会自动记录以下性能指标：
- 处理时间
- API调用成本
- 成功率
- 错误类型和频率

这些数据会保存在分析报告中，帮助您优化使用策略。
