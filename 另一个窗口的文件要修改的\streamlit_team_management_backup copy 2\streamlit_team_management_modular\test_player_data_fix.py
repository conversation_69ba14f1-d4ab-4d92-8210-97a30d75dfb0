#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Player数据转换修复
验证Player对象到字典的转换是否正确
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_player_to_dict():
    """测试Player对象转换为字典"""
    print("🧪 测试Player对象转换...")
    print("=" * 50)
    
    try:
        from models.player import Player
        
        # 创建测试Player对象
        player = Player(
            name="测试球员",
            jersey_number="10",
            photo="test_photo.jpg"
        )
        
        print(f"✅ Player对象创建成功: {player.name}")
        
        # 测试to_dict方法
        player_dict = player.to_dict()
        print(f"✅ to_dict转换成功")
        
        # 验证字典内容
        required_fields = ['name', 'jersey_number', 'photo']
        for field in required_fields:
            if field in player_dict:
                print(f"   ✅ {field}: {player_dict[field]}")
            else:
                print(f"   ❌ 缺少字段: {field}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Player转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_word_service_with_player_dict():
    """测试Word服务使用Player字典数据"""
    print("\n📄 测试Word服务使用Player字典...")
    print("=" * 50)
    
    try:
        from word_generator_service import WordGeneratorService
        from models.player import Player
        
        # 创建测试Player对象
        players_objects = [
            Player(name="字典测试球员1", jersey_number="10", photo="../word_zc/ai-football-generator/photos/player1.png"),
            Player(name="字典测试球员2", jersey_number="9", photo="../word_zc/ai-football-generator/photos/player2.jpg")
        ]
        
        # 转换为字典格式
        players_data = [player.to_dict() for player in players_objects]
        
        print(f"✅ 转换了 {len(players_data)} 个Player对象为字典")
        
        # 验证字典格式
        for i, player_dict in enumerate(players_data, 1):
            print(f"   球员{i}: {player_dict['name']} (#{player_dict['jersey_number']})")
        
        # 测试Word生成服务
        jar_path = "../word_zc/ai-football-generator/target/word-generator.jar"
        template_path = "../word_zc/ai-football-generator/template.docx"
        output_dir = "word_output"
        
        word_service = WordGeneratorService(jar_path, template_path, output_dir)
        
        team_data = {
            'name': 'Player字典测试队',
            'leader': '测试领队',
            'coach': '测试教练',
            'doctor': '测试队医'
        }
        
        # 生成Word报名表
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print("✅ Word报名表生成成功！")
            print(f"📁 文件路径: {result['file_path']}")
            
            if os.path.exists(result['file_path']):
                file_size = os.path.getsize(result['file_path'])
                print(f"📏 文件大小: {file_size / 1024:.1f} KB")
                return True
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print("❌ Word报名表生成失败")
            print(f"错误信息: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Word服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_validation():
    """测试数据验证"""
    print("\n🔍 测试数据验证...")
    print("=" * 50)
    
    try:
        from models.player import Player
        
        # 测试各种Player数据情况
        test_cases = [
            {"name": "正常球员", "jersey_number": "10", "photo": "photo.jpg"},
            {"name": "无照片球员", "jersey_number": "9", "photo": None},
            {"name": "空照片球员", "jersey_number": "8", "photo": ""},
        ]
        
        for i, case in enumerate(test_cases, 1):
            player = Player(**case)
            player_dict = player.to_dict()
            
            print(f"   测试用例{i}: {player_dict['name']}")
            print(f"      号码: {player_dict['jersey_number']}")
            print(f"      照片: {player_dict['photo']}")
            
            # 验证Word服务期望的字段
            if player_dict.get('name') and player_dict.get('jersey_number'):
                print(f"      ✅ 数据有效")
            else:
                print(f"      ❌ 数据无效")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据验证测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Player数据转换修复测试")
    print("=" * 60)
    print("验证Player对象到字典转换的修复")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("Player对象转换", test_player_to_dict),
        ("数据验证", test_data_validation),
        ("Word服务集成", test_word_service_with_player_dict),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 执行测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！Player数据转换修复成功！")
        print("\n✅ 修复内容:")
        print("   1. Player对象正确转换为字典格式")
        print("   2. Word生成服务可以处理Player字典数据")
        print("   3. AI聊天组件数据转换正常")
        print("   4. 错误'Player' object has no attribute 'get'已解决")
        print("\n💡 现在用户可以:")
        print("   1. 在任何界面中正常使用Word生成功能")
        print("   2. 不会再遇到Player对象属性错误")
        print("   3. 享受流畅的Word报名表生成体验")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
