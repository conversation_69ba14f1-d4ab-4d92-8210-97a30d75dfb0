#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI处理示例脚本
演示如何使用球队管理系统导出的数据进行AI处理
"""

import json
import os
from pathlib import Path
from PIL import Image
import datetime

def load_ai_data(ai_file_path):
    """加载AI处理数据"""
    try:
        with open(ai_file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载AI数据失败: {e}")
        return None

def process_team_photos(ai_data):
    """处理团队照片 - AI修图示例"""
    print("🎨 开始AI修图处理...")
    
    team_name = ai_data['team_info']['name']
    players = ai_data['players']
    
    processed_photos = []
    
    for player in players:
        photo_info = player['photo_info']
        
        if photo_info['exists']:
            print(f"处理球员: {player['name']} (#{player['jersey_number']})")
            print(f"照片路径: {photo_info['absolute_path']}")
            
            # 这里是AI修图的地方
            # 示例：加载图片并进行处理
            try:
                with Image.open(photo_info['absolute_path']) as img:
                    # AI修图处理（示例：调整尺寸）
                    processed_img = img.resize((300, 400), Image.Resampling.LANCZOS)
                    
                    # 保存处理后的照片
                    output_dir = f"ai_export/processed_photos/{team_name}"
                    os.makedirs(output_dir, exist_ok=True)
                    
                    output_path = os.path.join(output_dir, f"{player['ai_tags']['person_id']}_processed.jpg")
                    processed_img.save(output_path, 'JPEG', quality=90)
                    
                    processed_photos.append({
                        'player': player,
                        'original_path': photo_info['absolute_path'],
                        'processed_path': output_path,
                        'status': 'success'
                    })
                    
                    print(f"✅ 处理完成: {output_path}")
                    
            except Exception as e:
                print(f"❌ 处理失败: {e}")
                processed_photos.append({
                    'player': player,
                    'original_path': photo_info['absolute_path'],
                    'processed_path': None,
                    'status': 'failed',
                    'error': str(e)
                })
        else:
            print(f"❌ 照片不存在: {player['name']}")
    
    return processed_photos

def generate_word_form(ai_data, processed_photos):
    """生成Word报名表 - AI填表示例"""
    print("📝 开始生成Word报名表...")
    
    team_info = ai_data['team_info']
    players = ai_data['players']
    
    # 这里是AI填写Word表格的地方
    # 示例：生成报名表数据结构
    form_data = {
        'team_name': team_info['name'],
        'total_players': team_info['total_players'],
        'registration_date': datetime.datetime.now().strftime('%Y-%m-%d'),
        'players': []
    }
    
    for i, player in enumerate(players):
        # 查找对应的处理后照片
        processed_photo = None
        for photo in processed_photos:
            if photo['player']['id'] == player['id']:
                processed_photo = photo
                break
        
        player_form_data = {
            'sequence': i + 1,
            'name': player['name'],
            'jersey_number': player['jersey_number'],
            'photo_path': processed_photo['processed_path'] if processed_photo and processed_photo['status'] == 'success' else None,
            'person_id': player['ai_tags']['person_id'],
            'display_name': player['ai_tags']['display_name']
        }
        
        form_data['players'].append(player_form_data)
        print(f"📋 添加到报名表: {player_form_data['display_name']}")
    
    # 保存报名表数据
    output_path = f"ai_export/forms/team_{team_info['name']}_registration_form.json"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(form_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 报名表数据已生成: {output_path}")
    
    # 这里可以调用Word API或使用python-docx库生成实际的Word文档
    print("💡 提示: 可以使用python-docx库将数据填入Word模板")
    
    return form_data

def main():
    """主处理流程"""
    print("🤖 AI球队数据处理系统")
    print("=" * 50)
    
    # 1. 查找AI处理文件
    ai_export_dir = "ai_export"
    ai_files = [f for f in os.listdir(ai_export_dir) if f.endswith('_ai_ready.json')]
    
    if not ai_files:
        print("❌ 没有找到AI处理文件")
        return
    
    print(f"📁 找到 {len(ai_files)} 个AI处理文件:")
    for i, file in enumerate(ai_files):
        print(f"  {i+1}. {file}")
    
    # 2. 处理每个文件
    for ai_file in ai_files:
        print(f"\n🔄 处理文件: {ai_file}")
        print("-" * 30)
        
        ai_file_path = os.path.join(ai_export_dir, ai_file)
        
        # 加载数据
        ai_data = load_ai_data(ai_file_path)
        if not ai_data:
            continue
        
        print(f"📊 团队: {ai_data['team_info']['name']}")
        print(f"👥 球员数量: {ai_data['team_info']['total_players']}")
        print(f"🕒 数据时间: {ai_data['export_time']}")
        
        # AI修图处理
        processed_photos = process_team_photos(ai_data)
        
        # 生成Word报名表
        form_data = generate_word_form(ai_data, processed_photos)
        
        print(f"✅ {ai_data['team_info']['name']} 处理完成!")
    
    print("\n🎉 所有团队处理完成!")
    print("\n📁 输出文件结构:")
    print("ai_export/")
    print("├── processed_photos/")
    print("│   └── [团队名]/")
    print("│       └── [球员ID]_processed.jpg")
    print("└── forms/")
    print("    └── team_[团队名]_registration_form.json")

if __name__ == "__main__":
    main()
