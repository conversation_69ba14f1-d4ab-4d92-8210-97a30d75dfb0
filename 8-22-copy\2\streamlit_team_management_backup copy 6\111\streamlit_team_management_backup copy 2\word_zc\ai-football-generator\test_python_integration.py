#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python集成测试脚本
测试重构后的Word生成核心模块与Python的集成
"""

import os
import sys
import time
from pathlib import Path

def test_jpype_integration():
    """测试JPype集成"""
    try:
        print("🐍 开始Python集成测试...")
        print("=" * 50)
        
        # 1. 导入JPype
        print("📦 导入JPype...")
        try:
            import jpype
            print("✅ JPype导入成功")
        except ImportError:
            print("❌ JPype未安装，请运行: pip install JPype1")
            return False
        
        # 2. 设置Java类路径
        print("📁 设置Java类路径...")
        current_dir = Path(__file__).parent
        target_dir = current_dir / "target" / "classes"
        
        if not target_dir.exists():
            print("❌ Java类文件不存在，请先编译项目: mvn compile")
            return False
        
        classpath = [str(target_dir)]
        
        # 添加依赖JAR包
        lib_patterns = [
            "target/dependency/*.jar",
            "~/.m2/repository/com/deepoove/poi-tl/*/poi-tl-*.jar",
            "~/.m2/repository/org/apache/poi/poi/*/poi-*.jar",
            "~/.m2/repository/org/apache/poi/poi-ooxml/*/poi-ooxml-*.jar"
        ]
        
        for pattern in lib_patterns:
            import glob
            jars = glob.glob(str(current_dir / pattern))
            classpath.extend(jars)
        
        print(f"✅ 类路径设置完成，包含 {len(classpath)} 个路径")
        
        # 3. 启动JVM
        print("🚀 启动JVM...")
        if not jpype.isJVMStarted():
            # 使用简化的启动方式
            jpype.startJVM(jpype.getDefaultJVMPath(), f"-Djava.class.path={';'.join(classpath)}")
            print("✅ JVM启动成功")
        else:
            print("ℹ️ JVM已经启动")
        
        # 4. 导入Java类
        print("📥 导入Java类...")
        try:
            PythonIntegrationAdapter = jpype.JClass('PythonIntegrationAdapter')
            print("✅ PythonIntegrationAdapter类导入成功")
        except Exception as e:
            print(f"❌ Java类导入失败: {e}")
            return False
        
        # 5. 创建适配器实例
        print("🔧 创建适配器实例...")
        try:
            adapter = PythonIntegrationAdapter(
                "template.docx",
                "output",
                "photos"
            )
            print("✅ 适配器实例创建成功")
        except Exception as e:
            print(f"❌ 适配器实例创建失败: {e}")
            return False
        
        # 6. 测试连接
        print("🔗 测试连接...")
        try:
            result = adapter.testConnection()
            print(f"✅ 连接测试成功: {result}")
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            return False
        
        # 7. 准备测试数据
        print("📝 准备测试数据...")
        team_info = {
            "title": "2025年五人制足球比赛报名表",
            "organizationName": "Python测试团队",
            "teamLeader": "张三",
            "coach": "李四",
            "teamDoctor": "王五"
        }
        
        players_data = [
            {"number": "10", "name": "球员1", "photoPath": "photos/player1.png"},
            {"number": "9", "name": "球员2", "photoPath": "photos/player2.jpg"},
            {"number": "8", "name": "球员3", "photoPath": "photos/player3.jpg"},
        ]
        
        print(f"✅ 测试数据准备完成，队伍: {team_info['organizationName']}, 球员数: {len(players_data)}")
        
        # 8. 验证数据
        print("🔍 验证数据...")
        try:
            # 转换为Java格式
            java_team_info = jpype.java.util.HashMap()
            for key, value in team_info.items():
                java_team_info.put(key, value)
            
            java_players = jpype.java.util.ArrayList()
            for player in players_data:
                java_player = jpype.java.util.HashMap()
                for key, value in player.items():
                    java_player.put(key, value)
                java_players.add(java_player)
            
            is_valid = adapter.validatePythonData(java_team_info, java_players)
            print(f"✅ 数据验证结果: {'通过' if is_valid else '失败'}")
            
            if not is_valid:
                print("❌ 数据验证失败，无法继续测试")
                return False
                
        except Exception as e:
            print(f"❌ 数据验证失败: {e}")
            return False
        
        # 9. 生成Word文档
        print("📄 生成Word文档...")
        try:
            output_path = adapter.generateReportFromPython(java_team_info, java_players)
            
            if output_path:
                print(f"✅ Word文档生成成功: {output_path}")
                
                # 检查文件是否存在
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    print(f"📊 文件大小: {file_size} 字节")
                    print("🎉 Python集成测试完全成功！")
                    return True
                else:
                    print("❌ 生成的文件不存在")
                    return False
            else:
                print("❌ Word文档生成失败")
                return False
                
        except Exception as e:
            print(f"❌ Word文档生成失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Python集成测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        if 'jpype' in locals() and jpype.isJVMStarted():
            print("🧹 清理JVM资源...")
            # 注意：通常不需要手动关闭JVM，让Python进程结束时自动处理

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查Java环境
    try:
        import subprocess
        result = subprocess.run(['java', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Java环境正常")
        else:
            print("❌ Java环境异常")
            return False
    except Exception:
        print("❌ 未找到Java环境")
        return False
    
    # 检查Maven（可选）
    try:
        result = subprocess.run(['mvn', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Maven环境正常")
        else:
            print("⚠️ Maven环境异常，但可以继续测试")
    except Exception:
        print("⚠️ 未找到Maven环境，但可以继续测试")
    
    # 检查项目编译
    current_dir = Path(__file__).parent
    target_dir = current_dir / "target" / "classes"
    
    if target_dir.exists():
        print("✅ 项目已编译")
    else:
        print("❌ 项目未编译，请先运行: mvn compile")
        return False
    
    return True

def main():
    """主函数"""
    print("🧪 Word生成核心模块 - Python集成测试")
    print("=" * 60)
    
    # 检查前置条件
    if not check_prerequisites():
        print("❌ 前置条件检查失败")
        return
    
    print()
    
    # 运行集成测试
    success = test_jpype_integration()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 所有测试通过！模块化重构成功！")
        print("💡 现在可以将此模块集成到Python足球系统中")
    else:
        print("❌ 测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
