#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实换装演示
Real Fashion Try-On Demo
"""

import requests
import time
import os
from PIL import Image, ImageDraw

# API配置
API_KEY = "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o"
BASE_URL = "https://api.302.ai"

FASHION_TRYON_CONFIG = {
    "modelImgSegLabels": "10",    # 10-上衣
    "clothesImgSegLabels": "10"   # 10-上衣
}

TIMEOUT_CONFIG = {
    "request_timeout": 30,
    "task_check_interval": 30,
    "max_retry_attempts": 20
}

def create_realistic_jersey():
    """创建一个真实的球衣模板"""
    print("🎨 创建真实的球衣模板...")
    
    os.makedirs("real_demo", exist_ok=True)
    
    width, height = 512, 512
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 创建一个真实的足球球衣
    jersey_color = (220, 20, 60)  # 深红色
    
    # 球衣主体 - 更真实的形状
    main_body = [
        (120, 80),   # 左肩
        (160, 60),   # 左领
        (200, 60),   # 领口左
        (220, 70),   # 领口中
        (240, 60),   # 领口右
        (280, 60),   # 右领
        (320, 80),   # 右肩
        (360, 100),  # 右袖
        (360, 160),  # 右袖底
        (340, 180),  # 右腋下
        (340, 420),  # 右下
        (120, 420),  # 左下
        (120, 180),  # 左腋下
        (100, 160),  # 左袖底
        (100, 100),  # 左袖
    ]
    
    draw.polygon(main_body, fill=jersey_color, outline=(150, 0, 30), width=3)
    
    # 添加V领设计
    v_neck = [
        (200, 60),
        (240, 60),
        (230, 90),
        (210, 90)
    ]
    draw.polygon(v_neck, fill='white', outline=(150, 0, 30), width=2)
    
    # 添加球衣号码 "10"
    # 数字1
    draw.rectangle([200, 200, 210, 280], fill='white', outline='black', width=2)
    
    # 数字0
    draw.ellipse([220, 200, 260, 280], fill='white', outline='black', width=3)
    draw.ellipse([230, 210, 250, 270], fill=jersey_color)
    
    # 添加球衣条纹
    for i in range(3):
        y = 120 + i * 80
        draw.rectangle([130, y, 330, y + 10], fill=(180, 0, 40))
    
    # 添加袖口设计
    draw.rectangle([100, 150, 120, 170], fill='white', outline='black', width=2)
    draw.rectangle([340, 150, 360, 170], fill='white', outline='black', width=2)
    
    # 添加领口细节
    draw.arc([190, 50, 250, 110], 0, 180, fill='white', width=3)
    
    jersey_path = "real_demo/realistic_jersey.png"
    img.save(jersey_path)
    print(f"⚽ 创建球衣模板: {jersey_path}")
    
    return jersey_path

def create_business_shirt():
    """创建一个商务衬衫模板"""
    print("👔 创建商务衬衫模板...")
    
    width, height = 512, 512
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 浅蓝色商务衬衫
    shirt_color = (173, 216, 230)  # 浅蓝色
    
    # 衬衫主体
    shirt_body = [
        (130, 70),   # 左肩
        (170, 50),   # 左领
        (200, 50),   # 领口左
        (220, 60),   # 领口中
        (240, 50),   # 领口右
        (270, 50),   # 右领
        (310, 70),   # 右肩
        (350, 90),   # 右袖
        (350, 150),  # 右袖底
        (330, 170),  # 右腋下
        (330, 450),  # 右下
        (110, 450),  # 左下
        (110, 170),  # 左腋下
        (90, 150),   # 左袖底
        (90, 90),    # 左袖
    ]
    
    draw.polygon(shirt_body, fill=shirt_color, outline=(100, 150, 180), width=3)
    
    # 添加衬衫领子
    collar_left = [
        (170, 50), (200, 50), (190, 80), (160, 70)
    ]
    collar_right = [
        (240, 50), (270, 50), (280, 70), (250, 80)
    ]
    draw.polygon(collar_left, fill='white', outline=(100, 150, 180), width=2)
    draw.polygon(collar_right, fill='white', outline=(100, 150, 180), width=2)
    
    # 添加纽扣
    button_positions = [100, 140, 180, 220, 260, 300, 340, 380]
    for y in button_positions:
        draw.ellipse([215, y, 225, y + 10], fill='white', outline='gray', width=1)
    
    # 添加胸袋
    draw.rectangle([150, 120, 190, 160], fill=shirt_color, outline=(100, 150, 180), width=2)
    
    # 添加袖口
    draw.rectangle([90, 140, 110, 160], fill='white', outline=(100, 150, 180), width=2)
    draw.rectangle([330, 140, 350, 160], fill='white', outline=(100, 150, 180), width=2)
    
    shirt_path = "real_demo/business_shirt.png"
    img.save(shirt_path)
    print(f"👔 创建衬衫模板: {shirt_path}")
    
    return shirt_path

def perform_real_demo():
    """执行真实的换装演示"""
    print("🎯 开始真实换装演示")
    print("=" * 60)
    
    # 1. 选择模特照片
    model_image = "uploads/121311/05e367e3bc6641e58bdf1915d6dc5e2e.jpg"
    print(f"📸 模特照片: {model_image}")
    
    # 2. 创建两个不同的服装模板
    jersey_template = create_realistic_jersey()
    shirt_template = create_business_shirt()
    
    # 3. 演示1：换装成球衣
    print(f"\n🎯 演示1：换装成红色球衣")
    result1 = test_fashion_api(model_image, jersey_template, "demo1_jersey")
    
    if result1:
        print(f"✅ 球衣换装成功！结果: {result1}")
    else:
        print("❌ 球衣换装失败")
    
    # 4. 演示2：换装成商务衬衫
    print(f"\n🎯 演示2：换装成商务衬衫")
    result2 = test_fashion_api(model_image, shirt_template, "demo2_shirt")
    
    if result2:
        print(f"✅ 衬衫换装成功！结果: {result2}")
    else:
        print("❌ 衬衫换装失败")
    
    # 5. 创建对比图
    create_demo_comparison(model_image, jersey_template, shirt_template, result1, result2)

def test_fashion_api(model_image, clothes_image, demo_name):
    """测试换装API"""
    print(f"\n🚀 开始 {demo_name} 换装...")
    print(f"📸 模特图片: {model_image}")
    print(f"👕 服装图片: {clothes_image}")
    
    if not os.path.exists(model_image):
        print(f"❌ 模特图片不存在: {model_image}")
        return None
    
    if not os.path.exists(clothes_image):
        print(f"❌ 服装图片不存在: {clothes_image}")
        return None
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/create-task"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        with open(model_image, 'rb') as model_file, open(clothes_image, 'rb') as clothes_file:
            files = {
                'modelImageFile': (os.path.basename(model_image), model_file, 'image/jpeg'),
                'clothesImageFile': (os.path.basename(clothes_image), clothes_file, 'image/png')
            }
            
            data = FASHION_TRYON_CONFIG
            
            print("📤 发送换装任务请求...")
            response = requests.post(url, headers=headers, files=files, data=data, 
                                   timeout=TIMEOUT_CONFIG["request_timeout"])
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None
    
    print(f"📊 响应状态码: {response.status_code}")
    
    if response.status_code in [200, 201]:
        try:
            result = response.json()
            print(f"📄 响应内容: {result}")
            
            if result.get('code') == 200 and 'data' in result:
                task_id = result['data']['taskId']
                print(f"✅ 换装任务创建成功！任务ID: {task_id}")
                return wait_for_task_completion(task_id, demo_name)
            else:
                print(f"❌ 任务创建失败: {result}")
                return None
        except Exception as e:
            print(f"❌ 解析响应失败: {e}")
            return None
    else:
        print(f"❌ API请求失败: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        return None

def wait_for_task_completion(task_id, demo_name):
    """等待任务完成"""
    print(f"⏳ 等待 {demo_name} 任务完成...")
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/check-task-status"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    max_attempts = TIMEOUT_CONFIG["max_retry_attempts"]
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"🔄 第{attempt}次查询...")
        
        params = {"taskId": task_id}
        try:
            response = requests.get(url, headers=headers, params=params, 
                                  timeout=TIMEOUT_CONFIG["request_timeout"])
        except Exception as e:
            print(f"❌ 网络请求失败: {e}")
            time.sleep(TIMEOUT_CONFIG["task_check_interval"])
            continue
        
        if response.status_code == 200:
            try:
                result = response.json()
                status = result.get('data', 'UNKNOWN')
                
                print(f"📈 任务状态: {status}")
                
                if status == 'SUCCESS' and 'output' in result:
                    output = result['output']
                    result_url = output.get('resultUrl', '')
                    
                    print(f"🎉 {demo_name} 任务完成！")
                    print(f"🔗 结果图URL: {result_url}")
                    
                    return download_result_image(result_url, f"{demo_name}_result.png")
                    
                elif status in ['RUNNING', 'QUEUED', 'SUBMITTING']:
                    print(f"⏳ 任务进行中... 等待{TIMEOUT_CONFIG['task_check_interval']}秒后再次查询...")
                    time.sleep(TIMEOUT_CONFIG["task_check_interval"])
                    
                else:
                    print(f"❌ 任务失败，状态: {status}")
                    print(f"📄 完整响应: {result}")
                    return None
                    
            except Exception as e:
                print(f"❌ 解析响应失败: {e}")
                time.sleep(TIMEOUT_CONFIG["task_check_interval"])
                continue
        else:
            print(f"❌ 查询失败: {response.status_code}")
            time.sleep(TIMEOUT_CONFIG["task_check_interval"])
            continue
    
    print("⏰ 等待超时")
    return None

def download_result_image(url, filename):
    """下载结果图片"""
    try:
        print(f"📥 下载结果图片: {url}")
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            output_path = os.path.join("real_demo", filename)
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 图片下载成功: {output_path}")
            return output_path
        else:
            print(f"❌ 下载图片失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 下载图片异常: {e}")
        return None

def create_demo_comparison(model_image, jersey_template, shirt_template, result1, result2):
    """创建演示对比图"""
    print("\n📊 创建演示对比图...")
    
    try:
        # 加载所有图片
        original = Image.open(model_image).resize((200, 250))
        jersey_temp = Image.open(jersey_template).resize((200, 250))
        shirt_temp = Image.open(shirt_template).resize((200, 250))
        
        images = [original, jersey_temp, shirt_temp]
        titles = ["Original Model", "Jersey Template", "Shirt Template"]
        
        if result1 and os.path.exists(result1):
            result1_img = Image.open(result1).resize((200, 250))
            images.append(result1_img)
            titles.append("Jersey Result")
        
        if result2 and os.path.exists(result2):
            result2_img = Image.open(result2).resize((200, 250))
            images.append(result2_img)
            titles.append("Shirt Result")
        
        # 创建拼接图
        total_width = 200 * len(images) + 20 * (len(images) - 1)
        total_height = 300
        
        comparison = Image.new('RGB', (total_width, total_height), 'white')
        
        for i, img in enumerate(images):
            x = i * 220
            y = 30
            comparison.paste(img, (x, y))
            
            # 添加标题
            draw = ImageDraw.Draw(comparison)
            draw.text((x + 10, 10), titles[i], fill='black')
        
        comparison.save("real_demo/demo_comparison.png")
        print("📊 创建对比图: real_demo/demo_comparison.png")
        
    except Exception as e:
        print(f"❌ 创建对比图失败: {e}")

def main():
    """主函数"""
    print("🎯 真实换装演示")
    print("使用现有图片进行实际API调用")
    print("=" * 60)
    
    perform_real_demo()
    
    print("\n🎉 演示完成！")
    print("📁 查看 real_demo/ 文件夹中的结果")

if __name__ == "__main__":
    main()
