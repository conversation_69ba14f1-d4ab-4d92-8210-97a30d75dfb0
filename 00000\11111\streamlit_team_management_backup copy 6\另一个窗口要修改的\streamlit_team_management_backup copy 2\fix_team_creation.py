#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复团队创建问题
Fix Team Creation Issues
"""

import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'streamlit_team_management_modular'))

def fix_user_data_structure():
    """修复用户数据结构"""
    print("🔧 修复用户数据结构...")
    
    try:
        from config.settings import app_settings
        
        data_folder = app_settings.paths.DATA_FOLDER
        print(f"📂 数据文件夹: {data_folder}")
        
        # 确保数据文件夹存在
        os.makedirs(data_folder, exist_ok=True)
        
        # 查找所有用户文件夹
        user_folders = []
        if os.path.exists(data_folder):
            for item in os.listdir(data_folder):
                item_path = os.path.join(data_folder, item)
                if os.path.isdir(item_path):
                    user_folders.append(item)
        
        print(f"📋 发现用户文件夹: {user_folders}")
        
        # 为每个用户创建必要的子文件夹
        for user_id in user_folders:
            user_path = os.path.join(data_folder, user_id)
            
            # 创建teams文件夹
            teams_path = os.path.join(user_path, 'teams')
            os.makedirs(teams_path, exist_ok=True)
            
            # 创建players文件夹
            players_path = os.path.join(user_path, 'players')
            os.makedirs(players_path, exist_ok=True)
            
            # 创建photos文件夹
            photos_path = os.path.join(user_path, 'photos')
            os.makedirs(photos_path, exist_ok=True)
            
            print(f"✅ 用户 {user_id} 的文件夹结构已修复")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复用户数据结构失败: {e}")
        return False

def test_team_creation_with_user_id(user_id: str):
    """测试指定用户ID的团队创建"""
    print(f"\n🧪 测试用户 {user_id} 的团队创建...")
    
    try:
        # 模拟 streamlit session state
        class MockSessionState:
            def __init__(self, user_id):
                self.data = {'user_id': user_id}
                self.user_id = user_id
            
            def get(self, key, default=None):
                return self.data.get(key, default)
            
            def __setitem__(self, key, value):
                self.data[key] = value
            
            def __getitem__(self, key):
                return self.data[key]
            
            def __contains__(self, key):
                return key in self.data
        
        # 创建模拟的 streamlit 模块
        import streamlit as st
        st.session_state = MockSessionState(user_id)
        
        print(f"✅ 设置用户ID: {user_id}")
        
        # 导入服务
        from services.team_service import TeamService
        
        team_service = TeamService()
        
        # 测试获取用户ID
        current_user_id = team_service._get_current_user_id()
        print(f"✅ 获取到用户ID: {current_user_id}")
        
        # 测试创建团队
        test_team_name = f"测试球队_{user_id[-4:]}"
        print(f"🏗️ 尝试创建团队: {test_team_name}")
        
        success, message = team_service.create_team(test_team_name)
        
        if success:
            print(f"✅ 团队创建成功: {message}")
            
            # 验证团队文件是否存在
            from config.settings import app_settings
            team_file = os.path.join(
                app_settings.paths.DATA_FOLDER, 
                user_id, 
                'teams', 
                f'{test_team_name}.json'
            )
            
            if os.path.exists(team_file):
                print(f"✅ 团队文件已创建: {team_file}")
                
                # 读取并验证团队数据
                with open(team_file, 'r', encoding='utf-8') as f:
                    team_data = json.load(f)
                
                print(f"📋 团队数据: {json.dumps(team_data, ensure_ascii=False, indent=2)}")
                
            else:
                print(f"❌ 团队文件未找到: {team_file}")
                return False
        else:
            print(f"❌ 团队创建失败: {message}")
            return False
        
        # 测试获取团队列表
        teams = team_service.get_teams_list()
        print(f"📋 当前团队列表: {teams}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始修复团队创建问题\n")
    
    # 修复用户数据结构
    structure_ok = fix_user_data_structure()
    
    # 测试不同用户ID的团队创建
    test_user_ids = [
        'default_user',
        'user_8eef308875b0'  # 从错误信息中看到的用户ID
    ]
    
    test_results = {}
    for user_id in test_user_ids:
        test_results[user_id] = test_team_creation_with_user_id(user_id)
    
    print(f"\n{'='*60}")
    print(f"📊 修复结果:")
    print(f"📁 数据结构: {'✅ 正常' if structure_ok else '❌ 异常'}")
    
    for user_id, result in test_results.items():
        print(f"🧪 用户 {user_id}: {'✅ 正常' if result else '❌ 异常'}")
    
    print('='*60)
    
    all_ok = structure_ok and all(test_results.values())
    
    if all_ok:
        print("🎉 团队创建功能已修复！")
        print("\n💡 建议:")
        print("1. 重启 Streamlit 应用")
        print("2. 刷新浏览器页面")
        print("3. 重新尝试创建团队")
    else:
        print("⚠️ 仍有问题需要解决，请检查上述错误信息")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
