#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
队徽生成器用户界面组件
Team Logo Generator UI Component

提供完整的队徽生成用户界面
"""

import os
import streamlit as st
from PIL import Image
from typing import Optional

from services.team_logo_generator import team_logo_generator


class TeamLogoGeneratorUI:
    """队徽生成器UI组件"""
    
    def __init__(self):
        """初始化组件"""
        self.generator = team_logo_generator
    
    def render(self) -> None:
        """渲染队徽生成器界面"""
        st.header("🎨 AI队徽生成器")
        st.markdown("使用AI为您的球队生成专业的队徽设计")
        
        # 创建标签页
        tab1, tab2, tab3 = st.tabs(["🎨 生成队徽", "📁 队徽管理", "ℹ️ 使用说明"])
        
        with tab1:
            self._render_generator_tab()
        
        with tab2:
            self._render_management_tab()
        
        with tab3:
            self._render_help_tab()
    
    def _render_generator_tab(self) -> None:
        """渲染队徽生成标签页"""
        st.subheader("🎨 创建新队徽")
        
        # 输入表单
        with st.form("logo_generator_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                team_name = st.text_input(
                    "球队名称 *",
                    placeholder="例如: 雄鹰足球俱乐部",
                    help="输入您的球队名称"
                )
                
                team_style = st.selectbox(
                    "设计风格",
                    options=["现代", "传统", "简约", "复古"],
                    index=0,
                    help="选择队徽的整体设计风格"
                )
            
            with col2:
                color_preference = st.text_input(
                    "颜色偏好",
                    value="蓝色和白色",
                    placeholder="例如: 红色和金色",
                    help="描述您希望的主要颜色搭配"
                )
                
                include_elements = st.multiselect(
                    "包含元素（可选）",
                    options=["盾牌", "足球", "雄鹰", "狮子", "星星", "皇冠", "翅膀", "火焰"],
                    help="选择希望包含在队徽中的元素"
                )
            
            # 高级选项
            with st.expander("🔧 高级选项"):
                custom_description = st.text_area(
                    "自定义描述",
                    placeholder="如果您有特殊要求，请在这里详细描述...",
                    help="可以添加更具体的设计要求"
                )
                
                generate_variations = st.checkbox(
                    "生成多个变体",
                    help="生成同一设计的多个变体（需要更多时间和费用）"
                )
            
            # 生成按钮
            submitted = st.form_submit_button(
                "🎨 生成队徽",
                type="primary",
                use_container_width=True
            )
            
            if submitted:
                if not team_name.strip():
                    st.error("❌ 请输入球队名称")
                    return
                
                self._generate_logo(team_name, team_style, color_preference, 
                                  include_elements, custom_description, generate_variations)
    
    def _generate_logo(self, team_name: str, team_style: str, color_preference: str,
                      include_elements: list, custom_description: str, generate_variations: bool) -> None:
        """生成队徽"""
        # 构建完整的颜色描述
        if include_elements:
            elements_text = "，并包含以下元素: " + "、".join(include_elements)
            color_preference += elements_text
        
        if custom_description:
            color_preference += f"。额外要求: {custom_description}"
        
        # 显示生成进度
        progress_container = st.container()
        
        with progress_container:
            # 生成队徽
            result = self.generator.generate_team_logo_complete(
                team_name=team_name,
                team_style=team_style,
                color_preference=color_preference
            )
            
            if result.get("success"):
                self._display_generated_logo(result)
                
                # 如果需要生成变体
                if generate_variations:
                    st.info("🔄 正在生成设计变体...")
                    for i in range(2):  # 生成2个额外变体
                        variant_result = self.generator.generate_team_logo_complete(
                            team_name=f"{team_name}_变体{i+1}",
                            team_style=team_style,
                            color_preference=color_preference
                        )
                        if variant_result.get("success"):
                            st.write(f"**变体 {i+1}:**")
                            self._display_generated_logo(variant_result, show_title=False)
            else:
                st.error(f"❌ 生成失败: {result.get('error', '未知错误')}")
                
                # 如果有描述但没有图像，仍然显示描述
                if result.get("description"):
                    st.warning("⚠️ 图像生成失败，但已生成设计描述:")
                    st.text_area("设计描述", result["description"], height=200)
    
    def _display_generated_logo(self, result: dict, show_title: bool = True) -> None:
        """显示生成的队徽"""
        if show_title:
            st.success("🎉 队徽生成成功！")
        
        col1, col2 = st.columns([1, 1])
        
        with col1:
            if result.get("has_image") and result.get("image_path"):
                try:
                    image = Image.open(result["image_path"])
                    st.image(image, caption=f"{result['team_name']} 队徽", use_column_width=True)
                    
                    # 下载按钮
                    with open(result["image_path"], "rb") as file:
                        st.download_button(
                            label="📥 下载队徽",
                            data=file.read(),
                            file_name=f"{result['team_name']}_logo.png",
                            mime="image/png"
                        )
                except Exception as e:
                    st.error(f"❌ 无法显示图像: {e}")
            else:
                st.warning("⚠️ 图像生成失败")
        
        with col2:
            st.subheader("📝 设计说明")
            st.write(f"**球队名称:** {result['team_name']}")
            st.write(f"**设计风格:** {result.get('style', 'N/A')}")
            st.write(f"**主要颜色:** {result.get('colors', 'N/A')}")
            
            if result.get("description"):
                with st.expander("查看详细设计描述"):
                    st.text_area("", result["description"], height=200, key=f"desc_{result['team_name']}")
    
    def _render_management_tab(self) -> None:
        """渲染队徽管理标签页"""
        st.subheader("📁 队徽管理")
        
        # 获取所有生成的队徽
        logos = self.generator.list_generated_logos()
        
        if not logos:
            st.info("📭 还没有生成任何队徽")
            return
        
        st.write(f"共找到 {len(logos)} 个队徽文件")
        
        # 显示队徽列表
        for i, logo in enumerate(logos):
            with st.expander(f"🎨 {logo['filename']} ({self._format_file_size(logo['size'])})"):
                col1, col2, col3 = st.columns([2, 2, 1])
                
                with col1:
                    try:
                        image = Image.open(logo['path'])
                        st.image(image, width=200)
                    except Exception as e:
                        st.error(f"无法显示图像: {e}")
                
                with col2:
                    st.write(f"**文件名:** {logo['filename']}")
                    st.write(f"**大小:** {self._format_file_size(logo['size'])}")
                    st.write(f"**创建时间:** {logo['created_at'][:19].replace('T', ' ')}")
                    
                    # 下载按钮
                    try:
                        with open(logo['path'], "rb") as file:
                            st.download_button(
                                label="📥 下载",
                                data=file.read(),
                                file_name=logo['filename'],
                                mime="image/png",
                                key=f"download_{i}"
                            )
                    except Exception as e:
                        st.error(f"下载失败: {e}")
                
                with col3:
                    if st.button("🗑️ 删除", key=f"delete_{i}"):
                        if self.generator.delete_logo(logo['path']):
                            st.success("删除成功")
                            st.experimental_rerun()
                        else:
                            st.error("删除失败")
    
    def _render_help_tab(self) -> None:
        """渲染帮助标签页"""
        st.subheader("ℹ️ 使用说明")
        
        st.markdown("""
        ### 🎨 如何生成队徽
        
        1. **输入球队名称**: 这是必填项，将影响队徽的整体设计
        2. **选择设计风格**: 
           - **现代**: 简洁、几何化的设计
           - **传统**: 经典的足球队徽风格
           - **简约**: 极简主义设计
           - **复古**: 怀旧风格的设计
        3. **设置颜色偏好**: 描述您希望的主要颜色搭配
        4. **选择包含元素**: 可选择希望在队徽中出现的特定元素
        5. **点击生成**: 等待AI为您创建专业的队徽
        
        ### 🔧 高级功能
        
        - **自定义描述**: 可以添加更具体的设计要求
        - **生成变体**: 为同一设计生成多个不同的版本
        - **队徽管理**: 查看、下载和管理所有生成的队徽
        
        ### 💡 设计建议
        
        - 保持队名简洁，便于在队徽中展示
        - 选择2-3种主要颜色，避免过于复杂
        - 考虑队徽在不同尺寸下的可读性
        - 选择有意义的元素来体现球队特色
        
        ### ⚠️ 注意事项
        
        - 生成过程需要1-2分钟，请耐心等待
        - 生成的队徽仅供参考，可能需要进一步设计调整
        - 请确保有稳定的网络连接
        - 生成功能需要OpenAI API密钥配置
        """)
        
        # 技术信息
        with st.expander("🔧 技术信息"):
            st.markdown("""
            - **AI模型**: GPT-4 + DALL-E 3
            - **图像尺寸**: 1024x1024 像素
            - **支持格式**: PNG
            - **生成时间**: 通常1-2分钟
            - **存储位置**: data/team_logos/
            """)
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"


def render_team_logo_generator():
    """渲染队徽生成器（独立函数）"""
    ui = TeamLogoGeneratorUI()
    ui.render()
