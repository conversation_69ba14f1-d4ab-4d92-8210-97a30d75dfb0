#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球队服务
Team Service

提供球队相关的业务逻辑处理
"""

from typing import List, Optional, Tuple, Dict, Any
import streamlit as st
import os
import json
import logging
from datetime import datetime

from models.team import Team, TeamInfo
from data.team_repository import TeamRepository
from utils.validation import DataValidator
from utils.safe_file_manager import safe_file_manager
from utils.helpers import get_safe_team_name
# 缓存已移除以解决数据一致性问题
from config.constants import UIConstants
from config.settings import app_settings

# 配置日志
logger = logging.getLogger(__name__)


class TeamService:
    """球队服务"""
    
    def __init__(self):
        # 获取当前用户ID并创建用户特定的TeamRepository
        user_id = self._get_current_user_id()
        self.team_repo = TeamRepository(user_id)
    
    def get_teams_list(self) -> List[str]:
        """
        获取当前用户的球队列表

        Returns:
            List[str]: 球队名称列表
        """
        try:
            # 获取当前用户ID
            user_id = self._get_current_user_id()
            if user_id:
                return self.get_user_teams(user_id)
            else:
                # 兼容旧版本，返回全局球队列表
                return self.team_repo.get_teams_list()
        except Exception as e:
            logger.error(f"获取球队列表失败: {e}")
            safe_file_manager.handle_media_file_error(e, "获取球队列表")
            return []

    def get_teams_list_fresh(self) -> List[str]:
        """
        获取当前用户的球队列表（无缓存，实时获取）
        用于创建球队后立即刷新列表

        Returns:
            List[str]: 球队名称列表
        """
        try:
            # 获取当前用户ID
            user_id = self._get_current_user_id()
            if user_id:
                return self.get_user_teams_fresh(user_id)
            else:
                # 兼容旧版本，返回全局球队列表
                return self.team_repo.get_teams_list()
        except Exception as e:
            logger.error(f"获取球队列表失败: {e}")
            safe_file_manager.handle_media_file_error(e, "获取球队列表")
            return []

    def get_user_teams(self, user_id: str) -> List[str]:
        """
        获取指定用户的球队列表

        Args:
            user_id: 用户ID

        Returns:
            List[str]: 球队名称列表
        """
        try:
            user_teams_path = os.path.join(app_settings.paths.DATA_FOLDER, user_id, 'teams')

            if not safe_file_manager.safe_file_exists(user_teams_path):
                logger.info(f"用户球队目录不存在: {user_teams_path}")
                return []

            teams = []
            for filename in os.listdir(user_teams_path):
                if filename.endswith('.json'):
                    team_name = filename[:-5]  # 移除.json后缀
                    teams.append(team_name)

            return sorted(teams)
        except Exception as e:
            logger.error(f"获取用户球队列表失败: {user_id}, 错误: {e}")
            safe_file_manager.handle_media_file_error(e, f"获取用户球队: {user_id}")
            return []

    def get_user_teams_fresh(self, user_id: str) -> List[str]:
        """
        获取指定用户的球队列表（无缓存，实时获取）
        用于创建球队后立即刷新列表

        Args:
            user_id: 用户ID

        Returns:
            List[str]: 球队名称列表
        """
        try:
            user_teams_path = os.path.join(app_settings.paths.DATA_FOLDER, user_id, 'teams')

            if not safe_file_manager.safe_file_exists(user_teams_path):
                logger.info(f"用户球队目录不存在: {user_teams_path}")
                return []

            teams = []
            for filename in os.listdir(user_teams_path):
                if filename.endswith('.json'):
                    team_name = filename[:-5]  # 移除.json后缀
                    teams.append(team_name)

            return sorted(teams)
        except Exception as e:
            logger.error(f"获取用户球队列表失败(无缓存): {user_id}, 错误: {e}")
            safe_file_manager.handle_media_file_error(e, f"获取用户球队(无缓存): {user_id}")
            return []

    def get_team_display_names(self, teams: List[str]) -> List[str]:
        """
        获取球队显示名称列表
        
        Args:
            teams: 球队名称列表
            
        Returns:
            List[str]: 球队显示名称列表
        """
        display_names = []
        for team in teams:
            if team == 'default':
                display_names.append(UIConstants.DEFAULTS["team_display_name"])
            else:
                display_names.append(team)
        return display_names
    
    def load_team(self, team_name: str) -> Optional[Team]:
        """
        加载当前用户的球队数据

        Args:
            team_name: 球队名称

        Returns:
            Optional[Team]: 球队对象
        """
        user_id = self._get_current_user_id()
        if user_id:
            return self.load_team_for_user(user_id, team_name)
        else:
            # 兼容旧版本
            return self.team_repo.load_team(team_name)

    def load_team_for_user(self, user_id: str, team_name: str) -> Optional[Team]:
        """
        加载指定用户的球队数据

        Args:
            user_id: 用户ID
            team_name: 球队名称

        Returns:
            Optional[Team]: 球队对象
        """
        team_data = self.load_team_data_for_user(user_id, team_name)
        if team_data:
            return Team.from_dict(team_data)
        return None

    def load_team_data_for_user(self, user_id: str, team_name: str) -> Optional[Dict[str, Any]]:
        """
        加载指定用户的球队原始数据

        Args:
            user_id: 用户ID
            team_name: 球队名称

        Returns:
            Optional[Dict[str, Any]]: 球队数据
        """
        team_file = os.path.join(app_settings.paths.DATA_FOLDER, user_id, 'teams', f'{team_name}.json')

        try:
            if os.path.exists(team_file):
                with open(team_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            st.error(f"加载球队数据失败: {e}")

        return None

    def clear_team_cache(self, team_name: str) -> None:
        """
        清理指定球队的缓存数据（简化版本）

        Args:
            team_name: 球队名称
        """
        # 只清理session state中的相关缓存
        cache_keys_to_clear = [
            f'team_cache_{team_name}',
            f'teams_list_cache',
            'current_team_data'
        ]

        for key in cache_keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]
    
    def create_team(self, team_name: str) -> Tuple[bool, str]:
        """
        为当前用户创建新球队

        Args:
            team_name: 球队名称

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        user_id = self._get_current_user_id()
        if user_id:
            return self.create_team_for_user(user_id, team_name)
        else:
            # 兼容旧版本
            return self._create_team_legacy(team_name)

    def create_team_for_user(self, user_id: str, team_name: str) -> Tuple[bool, str]:
        """
        为指定用户创建新球队

        Args:
            user_id: 用户ID
            team_name: 球队名称

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        # 验证球队名称
        is_valid, error_msg = DataValidator.validate_team_name(team_name)
        if not is_valid:
            return False, error_msg

        # 检查球队是否已存在
        if self.team_exists_for_user(user_id, team_name):
            return False, UIConstants.STATUS_MESSAGES["error_team_exists"]

        # 创建球队数据
        team_data = {
            "players": [],
            "team_info": {
                "name": team_name,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "description": "",
                "coach": "",
                "contact": ""
            }
        }

        # 保存球队数据
        if self.save_team_data_for_user(user_id, team_name, team_data):
            # 清除相关缓存
            self._clear_teams_cache()
            success_msg = UIConstants.STATUS_MESSAGES["success_team_create"].format(name=team_name)
            return True, success_msg
        else:
            return False, UIConstants.STATUS_MESSAGES["error_save_failed"]
    
    def delete_team(self, team_name: str) -> Tuple[bool, str]:
        """
        删除当前用户的球队

        Args:
            team_name: 球队名称

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        if team_name == 'default':
            return False, "不能删除默认球队"

        user_id = self._get_current_user_id()
        if user_id:
            return self.delete_team_for_user(user_id, team_name)
        else:
            # 兼容旧版本，但使用安全的删除方法
            if self.team_repo.delete_team(team_name):
                return True, f"球队 {team_name} 删除成功"
            else:
                return False, "删除球队失败"

    def delete_team_for_user(self, user_id: str, team_name: str) -> Tuple[bool, str]:
        """
        删除指定用户的球队（安全方法）

        Args:
            user_id: 用户ID
            team_name: 球队名称

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        # 检查球队是否存在
        if not self.team_exists_for_user(user_id, team_name):
            return False, f"球队 {team_name} 不存在"

        # 删除球队数据文件
        team_file = os.path.join(app_settings.paths.DATA_FOLDER, user_id, 'teams', f'{get_safe_team_name(team_name)}.json')
        try:
            if os.path.exists(team_file):
                os.remove(team_file)

                # 清除相关缓存
                st.cache_data.clear()

                return True, f"球队 {team_name} 删除成功"
            else:
                return False, "球队文件不存在"
        except Exception as e:
            st.error(f"删除球队失败: {e}")
            return False, "删除球队失败"
    
    def get_team_stats(self, team_name: str) -> dict:
        """
        获取球队统计信息

        Args:
            team_name: 球队名称

        Returns:
            dict: 统计信息
        """
        # 调试信息
        user_id = self._get_current_user_id()
        st.write(f"🔍 TeamService调试 - 获取统计信息:")
        st.write(f"   球队名称: {team_name}")
        st.write(f"   当前用户ID: {user_id}")

        team = self.load_team(team_name)
        st.write(f"   加载的球队对象: {team}")

        if not team:
            st.write(f"   ❌ 球队对象为空，返回默认统计")
            return {
                'total_players': 0,
                'players_with_photos': 0,
                'completion_rate': 0.0,
                'is_complete': False
            }

        total = team.total_players
        with_photos = team.players_with_photos
        completion_rate = (with_photos / total * 100) if total > 0 else 0.0

        st.write(f"   ✅ 球队统计: 总数={total}, 有照片={with_photos}, 完成度={completion_rate:.1f}%")

        return {
            'total_players': total,
            'players_with_photos': with_photos,
            'completion_rate': completion_rate,
            'is_complete': with_photos == total and total > 0
        }
    
    def get_team_display_name(self, team_name: str) -> str:
        """
        获取球队显示名称
        
        Args:
            team_name: 球队名称
            
        Returns:
            str: 显示名称
        """
        if team_name == 'default':
            return UIConstants.DEFAULTS["team_display_name"]
        return team_name
    
    def team_exists(self, team_name: str) -> bool:
        """
        检查球队是否存在
        
        Args:
            team_name: 球队名称
            
        Returns:
            bool: 球队是否存在
        """
        return self.team_repo.team_exists(team_name)
    
    def get_team_info(self, team_name: str) -> Optional[TeamInfo]:
        """
        获取球队基本信息
        
        Args:
            team_name: 球队名称
            
        Returns:
            Optional[TeamInfo]: 球队信息
        """
        user_id = self._get_current_user_id()
        if user_id:
            team_data = self.load_team_data_for_user(user_id, team_name)
            if team_data and 'team_info' in team_data:
                return TeamInfo.from_dict(team_data['team_info'])
        else:
            # 兼容旧版本
            return self.team_repo.get_team_info(team_name)
        return None

    def save_team_data_for_user(self, user_id: str, team_name: str, team_data: Dict[str, Any]) -> bool:
        """
        保存指定用户的球队数据

        Args:
            user_id: 用户ID
            team_name: 球队名称
            team_data: 球队数据

        Returns:
            bool: 是否成功
        """
        try:
            # 确保用户目录存在
            user_teams_path = os.path.join(app_settings.paths.DATA_FOLDER, user_id, 'teams')
            os.makedirs(user_teams_path, exist_ok=True)

            # 保存球队数据
            team_file = os.path.join(user_teams_path, f'{team_name}.json')
            with open(team_file, 'w', encoding='utf-8') as f:
                json.dump(team_data, f, ensure_ascii=False, indent=2)

            return True
        except Exception as e:
            st.error(f"保存球队数据失败: {e}")
            return False

    def team_exists_for_user(self, user_id: str, team_name: str) -> bool:
        """
        检查指定用户的球队是否存在

        Args:
            user_id: 用户ID
            team_name: 球队名称

        Returns:
            bool: 球队是否存在
        """
        team_file = os.path.join(app_settings.paths.DATA_FOLDER, user_id, 'teams', f'{team_name}.json')
        return os.path.exists(team_file)

    def _get_current_user_id(self) -> Optional[str]:
        """
        获取当前用户ID

        Returns:
            Optional[str]: 用户ID
        """
        return st.session_state.get('user_id')

    def _clear_teams_cache(self):
        """清除球队相关的所有缓存"""
        try:
            # 清除Streamlit缓存
            st.cache_data.clear()

            # 清除可能的函数缓存
            if hasattr(self.get_teams_list, 'clear'):
                self.get_teams_list.clear()

            if hasattr(self.get_user_teams, 'clear'):
                self.get_user_teams.clear()

            # 清除session state中的相关缓存
            cache_keys = [
                'teams_cache',
                'current_team_cache',
                'team_display_names_cache',
                'team_stats_cache'
            ]

            for key in cache_keys:
                if key in st.session_state:
                    del st.session_state[key]

        except Exception as e:
            # 缓存清除失败不应该影响主要功能
            logger.warning(f"清除缓存时出现警告: {e}")
            pass

    def _create_team_legacy(self, team_name: str) -> Tuple[bool, str]:
        """
        兼容旧版本的创建球队方法

        Args:
            team_name: 球队名称

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        # 验证球队名称
        is_valid, error_msg = DataValidator.validate_team_name(team_name)
        if not is_valid:
            return False, error_msg

        # 检查球队是否已存在
        if self.team_repo.team_exists(team_name):
            return False, UIConstants.STATUS_MESSAGES["error_team_exists"]

        # 创建球队
        if self.team_repo.create_team(team_name):
            success_msg = UIConstants.STATUS_MESSAGES["success_team_create"].format(name=team_name)
            return True, success_msg
        else:
            return False, UIConstants.STATUS_MESSAGES["error_save_failed"]
