#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传测试页面
用于诊断文件上传组件问题
"""

import streamlit as st
import time

def main():
    st.set_page_config(
        page_title="文件上传测试",
        page_icon="📁",
        layout="wide"
    )
    
    st.title("📁 文件上传功能测试")
    st.markdown("---")
    
    # 测试1：最基础的文件上传
    st.header("测试1：基础文件上传")
    uploaded_file1 = st.file_uploader("选择一个文件", type=['png', 'jpg', 'jpeg'])
    if uploaded_file1:
        st.success(f"文件上传成功: {uploaded_file1.name}")
        st.image(uploaded_file1, width=200)
    
    st.markdown("---")
    
    # 测试2：多文件上传
    st.header("测试2：多文件上传")
    uploaded_files2 = st.file_uploader(
        "选择多个文件", 
        type=['png', 'jpg', 'jpeg'], 
        accept_multiple_files=True
    )
    if uploaded_files2:
        st.success(f"上传了 {len(uploaded_files2)} 个文件")
        for file in uploaded_files2:
            st.write(f"- {file.name}")
    
    st.markdown("---")
    
    # 测试3：带key的文件上传
    st.header("测试3：带唯一key的文件上传")
    if 'test_key' not in st.session_state:
        st.session_state.test_key = str(int(time.time()))
    
    uploaded_file3 = st.file_uploader(
        "选择文件（带key）", 
        type=['png', 'jpg', 'jpeg'],
        key=f"test_upload_{st.session_state.test_key}"
    )
    if uploaded_file3:
        st.success(f"文件上传成功: {uploaded_file3.name}")
    
    st.markdown("---")
    
    # 测试4：不同配置的文件上传
    st.header("测试4：不同配置测试")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("配置A：标准配置")
        uploaded_file4a = st.file_uploader(
            "标准配置",
            type=['png', 'jpg', 'jpeg', 'gif', 'bmp'],
            help="支持常见图片格式"
        )
        if uploaded_file4a:
            st.success("配置A上传成功")
    
    with col2:
        st.subheader("配置B：简化配置")
        uploaded_file4b = st.file_uploader("简化配置")
        if uploaded_file4b:
            st.success("配置B上传成功")
    
    st.markdown("---")
    
    # 诊断信息
    st.header("🔍 诊断信息")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Streamlit信息")
        st.write(f"Streamlit版本: {st.__version__}")
        st.write(f"Session State Keys: {list(st.session_state.keys())}")
    
    with col2:
        st.subheader("浏览器信息")
        st.write("请检查浏览器控制台是否有错误信息")
        st.write("按F12打开开发者工具查看Console")
    
    # 故障排除指南
    st.markdown("---")
    st.header("🛠️ 故障排除指南")
    
    with st.expander("如果文件上传按钮无反应，请尝试："):
        st.markdown("""
        **基础排查：**
        1. 刷新页面 (F5)
        2. 强制刷新 (Ctrl+Shift+R)
        3. 清除浏览器缓存
        4. 尝试无痕模式
        
        **浏览器检查：**
        1. 按F12打开开发者工具
        2. 查看Console标签页是否有红色错误
        3. 尝试不同的浏览器 (Chrome, Firefox, Edge)
        
        **网络检查：**
        1. 检查网络连接
        2. 关闭VPN或代理
        3. 禁用广告拦截器
        
        **系统检查：**
        1. 重启浏览器
        2. 重启Streamlit应用
        3. 检查文件权限
        """)
    
    # 重置按钮
    if st.button("🔄 重置所有状态"):
        for key in list(st.session_state.keys()):
            del st.session_state[key]
        st.rerun()

if __name__ == "__main__":
    main()
