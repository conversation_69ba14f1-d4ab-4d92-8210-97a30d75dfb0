"""
OpenAI结构化输出和函数调用配置
集成自ai_data_collector_test，适配用户隔离机制
"""

from typing import Dict, List, Any

# 球队信息JSON Schema
TEAM_INFO_SCHEMA = {
    "type": "object",
    "properties": {
        "basic_info": {
            "type": "object",
            "properties": {
                "team_name": {
                    "type": "string",
                    "description": "单位名称（球队名称）"
                },
                "contact_person": {
                    "type": "string",
                    "description": "球队联系人"
                },
                "contact_phone": {
                    "type": "string",
                    "pattern": "^1[3-9]\\d{9}$",
                    "description": "联系电话"
                },
                "leader_name": {
                    "type": "string",
                    "description": "领队姓名"
                },
                "team_doctor": {
                    "type": "string",
                    "description": "队医姓名"
                }
            },
            "required": ["team_name", "contact_person", "contact_phone", "leader_name", "team_doctor"],
            "additionalProperties": False
        },
        "kit_colors": {
            "type": "object",
            "properties": {
                "jersey_color": {
                    "type": "string",
                    "description": "球衣颜色（必填）"
                },
                "shorts_color": {
                    "type": "string",
                    "description": "球裤颜色（可选，AI自动填充）"
                },
                "socks_color": {
                    "type": "string",
                    "description": "球袜颜色（可选，AI自动填充）"
                },
                "goalkeeper_kit_color": {
                    "type": "string",
                    "description": "守门员服装颜色（可选，AI自动填充）"
                }
            },
            "required": ["jersey_color"],
            "additionalProperties": False
        },
        "team_logo": {
            "type": "object",
            "properties": {
                "has_logo": {
                    "type": "boolean",
                    "description": "是否有队徽"
                },
                "logo_type": {
                    "type": "string",
                    "description": "队徽类型"
                },
                "logo_description": {
                    "type": "string",
                    "description": "队徽描述"
                },
                "logo_file_path": {
                    "type": "string",
                    "description": "队徽文件路径"
                }
            },
            "additionalProperties": False
        },
        "organization": {
            "type": "object",
            "properties": {
                "seal_required": {
                    "type": "boolean",
                    "default": True,
                    "description": "是否需要盖章"
                },
                "seal_notes": {
                    "type": "string",
                    "default": "此处需要在打印后加盖单位公章",
                    "description": "盖章说明"
                }
            },
            "additionalProperties": False
        },
        "additional_info": {
            "type": "object",
            "properties": {
                "coach_name": {
                    "type": "string",
                    "description": "教练姓名（可选）"
                },
                "notes": {
                    "type": "string",
                    "description": "备注信息"
                }
            },
            "additionalProperties": False
        }
    },
    "required": ["basic_info", "kit_colors"],
    "additionalProperties": False
}

# Function Calling 函数定义
FUNCTION_DEFINITIONS = [
    {
        "type": "function",
        "name": "save_team_info",
        "description": "保存球队信息到数据库",
        "parameters": {
            "type": "object",
            "properties": {
                "team_data": TEAM_INFO_SCHEMA,
                "team_id": {
                    "type": "string",
                    "description": "球队ID，如果是新球队则自动生成"
                }
            },
            "required": ["team_data"]
        }
    },
    {
        "type": "function",
        "name": "get_team_info",
        "description": "获取球队信息",
        "parameters": {
            "type": "object",
            "properties": {
                "team_id": {
                    "type": "string",
                    "description": "球队ID"
                }
            },
            "required": ["team_id"]
        }
    },
    {
        "type": "function",
        "name": "extract_team_info_from_text",
        "description": "从用户输入文本中提取球队信息",
        "parameters": {
            "type": "object",
            "properties": {
                "user_text": {
                    "type": "string",
                    "description": "用户输入的文本"
                }
            },
            "required": ["user_text"]
        }
    }
]

# 球员信息JSON Schema
PLAYER_INFO_SCHEMA = {
    "type": "object",
    "properties": {
        "basic_info": {
            "type": "object",
            "properties": {
                "name": {"type": "string", "description": "球员姓名"},
                "jersey_number": {"type": "string", "description": "球衣号码"},
                "position": {"type": "string", "description": "场上位置"},
                "birth_date": {"type": "string", "description": "出生日期"}
            },
            "required": ["name", "jersey_number"]
        },
        "contact_info": {
            "type": "object",
            "properties": {
                "phone": {"type": "string", "description": "联系电话"},
                "email": {"type": "string", "description": "电子邮箱"},
                "address": {"type": "string", "description": "联系地址"}
            }
        },
        "football_info": {
            "type": "object",
            "properties": {
                "preferred_foot": {"type": "string", "enum": ["左脚", "右脚", "双脚"], "description": "惯用脚"},
                "height": {"type": "string", "description": "身高"},
                "weight": {"type": "string", "description": "体重"},
                "experience": {"type": "string", "description": "足球经验"}
            }
        }
    },
    "required": ["basic_info"],
    "additionalProperties": False
}

# 函数调用定义
FUNCTION_DEFINITIONS = [
    {
        "type": "function",
        "function": {
            "name": "extract_team_info",
            "description": "从用户输入中智能提取球队信息，支持自然语言解析",
            "parameters": {
                "type": "object",
                "properties": {
                    "extracted_info": TEAM_INFO_SCHEMA,
                    "confidence": {"type": "number", "minimum": 0, "maximum": 1, "description": "提取信息的置信度(0-1)"},
                    "missing_fields": {"type": "array", "items": {"type": "string"}, "description": "缺失的重要字段列表"},
                    "suggestions": {"type": "array", "items": {"type": "string"}, "description": "改进建议"}
                },
                "required": ["extracted_info", "confidence"]
            }
        }
    },
    {
        "type": "function", 
        "function": {
            "name": "extract_player_info",
            "description": "从用户输入中智能提取球员信息，支持批量处理",
            "parameters": {
                "type": "object",
                "properties": {
                    "players": {"type": "array", "items": PLAYER_INFO_SCHEMA, "description": "提取的球员信息列表"},
                    "confidence": {"type": "number", "minimum": 0, "maximum": 1, "description": "提取信息的置信度(0-1)"},
                    "validation_errors": {"type": "array", "items": {"type": "string"}, "description": "验证错误列表"}
                },
                "required": ["players", "confidence"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "validate_team_data",
            "description": "验证球队数据的完整性和一致性",
            "parameters": {
                "type": "object",
                "properties": {
                    "team_name": {"type": "string", "description": "要验证的球队名称"},
                    "check_completeness": {"type": "boolean", "description": "是否检查数据完整性"},
                    "check_consistency": {"type": "boolean", "description": "是否检查数据一致性"}
                },
                "required": ["team_name"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "generate_team_suggestions",
            "description": "基于当前球队状态生成智能建议",
            "parameters": {
                "type": "object",
                "properties": {
                    "team_name": {"type": "string", "description": "球队名称"},
                    "focus_area": {"type": "string", "enum": ["球员管理", "比赛准备", "数据完整性", "照片处理"], "description": "建议重点领域"}
                },
                "required": ["team_name"]
            }
        }
    },

    # 原始系统的核心数据管理函数
    {
        "type": "function",
        "function": {
            "name": "save_team_info",
            "description": "保存球队信息到数据库",
            "parameters": {
                "type": "object",
                "properties": {
                    "team_data": TEAM_INFO_SCHEMA,
                    "team_id": {"type": "string", "description": "球队ID，如果是新球队则自动生成"}
                },
                "required": ["team_data"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "save_player_info",
            "description": "保存球员信息到数据库",
            "parameters": {
                "type": "object",
                "properties": {
                    "player_data": PLAYER_INFO_SCHEMA,
                    "team_id": {"type": "string", "description": "所属球队ID"}
                },
                "required": ["player_data", "team_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_team_info",
            "description": "获取球队信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "team_id": {"type": "string", "description": "球队ID"}
                },
                "required": ["team_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_player_list",
            "description": "获取球队的球员列表",
            "parameters": {
                "type": "object",
                "properties": {
                    "team_id": {"type": "string", "description": "球队ID"}
                },
                "required": ["team_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "check_data_completeness",
            "description": "检查球队数据完整性",
            "parameters": {
                "type": "object",
                "properties": {
                    "team_id": {"type": "string", "description": "球队ID"}
                },
                "required": ["team_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "validate_jersey_number",
            "description": "验证球衣号码是否可用",
            "parameters": {
                "type": "object",
                "properties": {
                    "team_id": {"type": "string", "description": "球队ID"},
                    "jersey_number": {"type": "integer", "description": "球衣号码"}
                },
                "required": ["team_id", "jersey_number"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "generate_team_logo",
            "description": "为球队生成队徽描述",
            "parameters": {
                "type": "object",
                "properties": {
                    "team_name": {"type": "string", "description": "球队名称"},
                    "team_style": {"type": "string", "enum": ["现代", "传统", "简约", "复古"], "description": "队徽风格"},
                    "color_preference": {"type": "string", "description": "颜色偏好"}
                },
                "required": ["team_name"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "extract_team_info_from_text",
            "description": "从用户的自然语言文本中智能提取球队信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_text": {"type": "string", "description": "用户输入的文本"}
                },
                "required": ["user_text"]
            }
        }
    }
]

# 结构化输出响应格式
STRUCTURED_OUTPUT_FORMAT = {
    "type": "json_schema",
    "json_schema": {
        "name": "ai_response",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "response_type": {
                    "type": "string",
                    "enum": ["information_request", "data_extraction", "suggestion", "confirmation", "error"],
                    "description": "响应类型"
                },
                "message": {
                    "type": "string",
                    "description": "AI回复消息"
                },
                "extracted_data": {
                    "type": "object",
                    "description": "提取的结构化数据",
                    "properties": {
                        "team_name": {
                            "type": "string",
                            "description": "球队名称"
                        },
                        "contact_info": {
                            "type": "string",
                            "description": "联系信息"
                        },
                        "players": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {
                                        "type": "string",
                                        "description": "球员姓名"
                                    },
                                    "number": {
                                        "type": "string",
                                        "description": "球衣号码"
                                    }
                                },
                                "required": ["name", "number"],
                                "additionalProperties": False
                            }
                        }
                    },
                    "required": ["team_name", "contact_info", "players"],
                    "additionalProperties": False
                },
                "next_action": {
                    "type": "string",
                    "description": "建议的下一步操作"
                },
                "confidence": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 1,
                    "description": "响应置信度"
                }
            },
            "required": ["response_type", "message", "extracted_data", "next_action", "confidence"],
            "additionalProperties": False
        }
    }
}

# AI配置
AI_CONFIG = {
    "enable_structured_outputs": True,  # 重新启用结构化输出
    "enable_function_calling": True,
    "max_tokens": 4000,
    "temperature": 0.7,
    "model": "gpt-4o-2024-08-06"  # 使用支持结构化输出的模型版本
}
