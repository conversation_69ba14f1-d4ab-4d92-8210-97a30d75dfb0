#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终集成测试
验证Word生成功能的完整集成
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_word_service_direct():
    """直接测试Word生成服务"""
    print("🧪 直接测试Word生成服务...")
    print("=" * 50)
    
    try:
        from word_generator_service import WordGeneratorService
        
        # 初始化服务
        jar_path = "../word_zc/ai-football-generator/target/word-generator.jar"
        template_path = "../word_zc/ai-football-generator/template.docx"
        output_dir = "word_output"
        
        word_service = WordGeneratorService(jar_path, template_path, output_dir)
        print("✅ Word生成服务初始化成功")
        
        # 准备测试数据
        team_data = {
            'name': '最终测试球队',
            'leader': '测试领队',
            'coach': '测试教练',
            'doctor': '测试队医'
        }
        
        players_data = [
            {
                'name': '最终测试球员1',
                'jersey_number': '10',
                'photo': '../word_zc/ai-football-generator/photos/player1.png'
            },
            {
                'name': '最终测试球员2', 
                'jersey_number': '9',
                'photo': '../word_zc/ai-football-generator/photos/player2.jpg'
            }
        ]
        
        print(f"✅ 测试数据准备完成 - 球队: {team_data['name']}, 球员: {len(players_data)}人")
        
        # 生成Word报名表
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print("✅ Word报名表生成成功！")
            print(f"📁 文件路径: {result['file_path']}")
            
            # 检查文件是否存在
            if os.path.exists(result['file_path']):
                file_size = os.path.getsize(result['file_path'])
                print(f"📏 文件大小: {file_size / 1024:.1f} KB")
                return True
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print("❌ Word报名表生成失败")
            print(f"错误信息: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_import():
    """测试组件导入"""
    print("\n🔧 测试组件导入...")
    print("=" * 50)
    
    try:
        # 模拟streamlit环境
        class MockStreamlit:
            @staticmethod
            def error(msg): print(f"❌ {msg}")
            @staticmethod
            def warning(msg): print(f"⚠️ {msg}")
            @staticmethod
            def info(msg): print(f"ℹ️ {msg}")
            @staticmethod
            def success(msg): print(f"✅ {msg}")
            @staticmethod
            def markdown(msg): print(f"📝 {msg}")
            @staticmethod
            def progress(val): return MockProgress()
            @staticmethod
            def empty(): return MockEmpty()
            @staticmethod
            def container(): return MockContainer()
            @staticmethod
            def columns(n): return [MockColumn() for _ in range(n)]
            @staticmethod
            def button(label, **kwargs): return False
            @staticmethod
            def download_button(**kwargs): return False
            @staticmethod
            def expander(label, **kwargs): return MockExpander()
        
        class MockProgress:
            def progress(self, val): pass
        
        class MockEmpty:
            def text(self, msg): print(f"📄 {msg}")
        
        class MockContainer:
            def __enter__(self): return self
            def __exit__(self, *args): pass
        
        class MockColumn:
            def __enter__(self): return self
            def __exit__(self, *args): pass
        
        class MockExpander:
            def __enter__(self): return self
            def __exit__(self, *args): pass
        
        # 替换streamlit模块
        sys.modules['streamlit'] = MockStreamlit()
        
        # 测试Word生成器组件导入
        from components.word_generator import create_word_generator_component
        print("✅ Word生成器组件导入成功")
        
        # 创建组件实例
        word_generator = create_word_generator_component()
        print("✅ Word生成器组件创建成功")
        
        # 检查组件属性
        if hasattr(word_generator, 'word_service'):
            print("✅ Word生成器包含word_service属性")
            return True
        else:
            print("❌ Word生成器缺少word_service属性")
            return False
            
    except Exception as e:
        print(f"❌ 组件导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """测试配置"""
    print("\n⚙️ 测试配置...")
    print("=" * 50)
    
    try:
        from config.settings import app_settings
        
        # 检查Word生成器配置
        if hasattr(app_settings, 'word_generator'):
            print("✅ Word生成器配置存在")
            
            paths = app_settings.word_generator.get_absolute_paths()
            for key, path in paths.items():
                exists = "✅" if os.path.exists(path) else "❌"
                print(f"   {key}: {exists} {path}")
            
            return True
        else:
            print("❌ Word生成器配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_java_jar():
    """测试Java JAR文件"""
    print("\n☕ 测试Java JAR文件...")
    print("=" * 50)
    
    try:
        jar_path = "../word_zc/ai-football-generator/target/word-generator.jar"
        
        if os.path.exists(jar_path):
            print(f"✅ JAR文件存在: {jar_path}")
            
            # 检查文件大小
            file_size = os.path.getsize(jar_path)
            print(f"📏 JAR文件大小: {file_size / 1024 / 1024:.1f} MB")
            
            if file_size > 1024 * 1024:  # 大于1MB
                print("✅ JAR文件大小正常")
                return True
            else:
                print("❌ JAR文件可能不完整")
                return False
        else:
            print(f"❌ JAR文件不存在: {jar_path}")
            return False
            
    except Exception as e:
        print(f"❌ JAR文件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 最终集成测试套件")
    print("=" * 60)
    print("验证Word生成功能的完整集成")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("Java JAR文件", test_java_jar),
        ("配置系统", test_configuration),
        ("组件导入", test_component_import),
        ("Word生成服务", test_word_service_direct),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 执行测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 最终测试结果:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！Word生成功能集成成功！")
        print("\n🚀 功能特性:")
        print("   ✅ Python ↔ Java Subprocess调用")
        print("   ✅ JSON数据传递和解析")
        print("   ✅ Word文档自动生成")
        print("   ✅ 球员照片处理和嵌入")
        print("   ✅ 文件管理和下载")
        print("   ✅ 错误处理和用户反馈")
        print("   ✅ Streamlit UI组件集成")
        print("\n💡 用户现在可以:")
        print("   1. 在AI助手界面中看到Word生成面板")
        print("   2. 一键生成包含球员信息和照片的专业报名表")
        print("   3. 直接下载生成的Word文档")
        print("   4. 管理和查看历史生成的文件")
        print("\n🎯 集成完成！系统已准备就绪！")
    else:
        print("⚠️ 部分测试失败，请检查相关配置")
        print("\n🔧 可能的解决方案:")
        print("   1. 确保Java环境已正确安装")
        print("   2. 重新编译Java项目生成JAR文件")
        print("   3. 检查文件路径配置")
        print("   4. 验证依赖库是否完整")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
