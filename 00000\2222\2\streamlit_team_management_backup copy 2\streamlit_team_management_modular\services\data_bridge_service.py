#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据桥接服务
Data Bridge Service

连接AI聊天数据和换装功能，实现数据格式转换和映射
"""

import os
import json
import re
import streamlit as st
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from services.ai_service import AIService
from services.team_service import TeamService
from services.player_service import PlayerService
from services.auth_service import AuthService


class DataBridgeService:
    """数据桥接服务 - 连接AI数据和换装功能"""
    
    def __init__(self, user_id: str = None):
        """初始化数据桥接服务"""
        self.auth_service = AuthService()
        self.user_id = user_id or self.auth_service.get_current_user_id()
        
        self.ai_service = AIService(self.user_id)
        self.team_service = TeamService()
        self.player_service = PlayerService()
    
    def extract_team_info_from_chat(self, chat_messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        从AI聊天记录中提取球队信息
        
        Args:
            chat_messages: AI聊天消息列表
            
        Returns:
            Dict: 提取的球队信息
        """
        try:
            # 过滤用户和AI的对话内容
            conversation_text = ""
            for msg in chat_messages:
                if msg["role"] in ["user", "assistant"]:
                    conversation_text += f"{msg['role']}: {msg['content']}\n"
            
            # 使用AI服务提取结构化信息
            if self.ai_service.has_enhanced_features():
                extracted_info = self.ai_service.extract_team_info_from_text(conversation_text)
            else:
                # 回退到基础提取
                extracted_info = self._basic_text_extraction(conversation_text)
            
            return extracted_info
            
        except Exception as e:
            st.error(f"从聊天记录提取信息失败: {e}")
            return {}
    
    def _basic_text_extraction(self, text: str) -> Dict[str, Any]:
        """基础文本信息提取"""
        extracted = {
            "team_name": "",
            "players": [],
            "competition_info": {},
            "extraction_confidence": "low"
        }
        
        # 简单的正则表达式提取
        # 提取球队名称
        team_patterns = [
            r"球队[名称叫做是]?[:：]?\s*([^\n\r，。！？]+)",
            r"我们?[的]?球队[名称叫做是]?\s*([^\n\r，。！？]+)",
            r"([^\n\r，。！？]+)[球队队伍]"
        ]
        
        for pattern in team_patterns:
            match = re.search(pattern, text)
            if match:
                extracted["team_name"] = match.group(1).strip()
                break
        
        # 提取球员信息
        player_patterns = [
            r"球员[:：]?\s*([^\n\r]+)",
            r"队员[:：]?\s*([^\n\r]+)",
            r"成员[:：]?\s*([^\n\r]+)"
        ]
        
        for pattern in player_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                # 分割球员名称
                names = re.split(r'[，,、\s]+', match.strip())
                for name in names:
                    if name and len(name) > 1:
                        extracted["players"].append({
                            "name": name.strip(),
                            "jersey_number": "",
                            "position": ""
                        })
        
        return extracted
    
    def convert_to_fashion_format(self, team_name: str) -> Dict[str, Any]:
        """
        将球队数据转换为换装所需的格式
        
        Args:
            team_name: 球队名称
            
        Returns:
            Dict: 换装格式的数据
        """
        try:
            # 1. 获取球队的球员数据
            players = self.player_service.get_players(team_name)
            
            # 2. 构建换装数据格式
            fashion_data = {
                "team_info": {
                    "name": team_name,
                    "total_players": len(players)
                },
                "players_with_photos": [],
                "players_without_photos": [],
                "photo_paths": [],
                "ready_for_fashion": False
            }
            
            # 3. 分类球员（有照片 vs 无照片）
            for player in players:
                player_data = {
                    "id": player.get("id"),
                    "name": player.get("name"),
                    "jersey_number": player.get("jersey_number"),
                    "photo_path": None
                }
                
                # 检查照片路径
                photo_path = self._get_player_photo_path(team_name, player)
                if photo_path and os.path.exists(photo_path):
                    player_data["photo_path"] = photo_path
                    fashion_data["players_with_photos"].append(player_data)
                    fashion_data["photo_paths"].append(photo_path)
                else:
                    fashion_data["players_without_photos"].append(player_data)
            
            # 4. 判断是否准备好换装
            fashion_data["ready_for_fashion"] = len(fashion_data["players_with_photos"]) > 0
            
            return fashion_data
            
        except Exception as e:
            st.error(f"转换换装格式失败: {e}")
            return {"ready_for_fashion": False, "error": str(e)}
    
    def _get_player_photo_path(self, team_name: str, player: Dict) -> Optional[str]:
        """获取球员照片路径"""
        try:
            # 构建可能的照片路径
            uploads_dir = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "uploads",
                team_name
            )
            
            if not os.path.exists(uploads_dir):
                return None
            
            # 查找球员照片文件
            player_id = player.get("id", "")
            player_name = player.get("name", "")
            
            # 遍历上传目录中的文件
            for filename in os.listdir(uploads_dir):
                if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                    file_path = os.path.join(uploads_dir, filename)
                    
                    # 简单的文件名匹配（可以改进为更智能的匹配）
                    if player_id in filename or player_name in filename:
                        return file_path
            
            # 如果没有找到特定匹配，返回第一个图片文件（临时方案）
            for filename in os.listdir(uploads_dir):
                if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                    return os.path.join(uploads_dir, filename)
            
            return None
            
        except Exception as e:
            return None
    
    def create_fashion_ready_export(self, team_name: str) -> Dict[str, Any]:
        """
        创建换装就绪的导出数据
        
        Args:
            team_name: 球队名称
            
        Returns:
            Dict: 导出结果
        """
        try:
            # 1. 转换数据格式
            fashion_data = self.convert_to_fashion_format(team_name)
            
            if not fashion_data["ready_for_fashion"]:
                return {
                    "success": False,
                    "message": "球队数据不完整，无法进行换装",
                    "fashion_data": fashion_data
                }
            
            # 2. 创建导出文件
            export_data = {
                "team_info": {
                    "name": team_name,
                    "display_name": team_name,
                    "created_at": datetime.now().isoformat(),
                    "total_players": fashion_data["team_info"]["total_players"]
                },
                "players": [],
                "export_time": datetime.now().isoformat(),
                "ai_processing": {
                    "status": "ready",
                    "next_steps": ["fashion_tryon"],
                    "photo_folder": f"uploads/{team_name}",
                    "export_folder": "ai_export"
                }
            }
            
            # 3. 添加球员信息
            for player in fashion_data["players_with_photos"]:
                export_data["players"].append({
                    "id": player["id"],
                    "name": player["name"],
                    "jersey_number": player["jersey_number"],
                    "photo_info": {
                        "filename": os.path.basename(player["photo_path"]),
                        "relative_path": os.path.relpath(player["photo_path"]),
                        "absolute_path": player["photo_path"],
                        "exists": True
                    },
                    "created_at": datetime.now().isoformat(),
                    "ai_tags": {
                        "person_id": f"{team_name}_{player['jersey_number']}_{player['name']}",
                        "display_name": f"{player['name']} (#{player['jersey_number']})",
                        "team": team_name
                    }
                })
            
            # 4. 保存导出文件
            export_dir = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "ai_export"
            )
            os.makedirs(export_dir, exist_ok=True)
            
            export_file = os.path.join(export_dir, f"team_{team_name}_ai_ready.json")
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return {
                "success": True,
                "message": f"换装数据导出成功，{len(export_data['players'])}名球员准备就绪",
                "export_file": export_file,
                "fashion_data": fashion_data,
                "export_data": export_data
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"创建换装导出失败: {str(e)}",
                "error": str(e)
            }
    
    def sync_chat_to_team_data(self, team_name: str, chat_messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        将AI聊天数据同步到球队数据系统
        
        Args:
            team_name: 球队名称
            chat_messages: 聊天消息
            
        Returns:
            Dict: 同步结果
        """
        try:
            # 1. 从聊天中提取信息
            extracted_info = self.extract_team_info_from_chat(chat_messages)
            
            # 2. 更新球队数据
            sync_results = {
                "team_updated": False,
                "players_added": 0,
                "players_updated": 0,
                "export_created": False
            }
            
            # 3. 如果提取到球员信息，添加到系统中
            if extracted_info.get("players"):
                for player_info in extracted_info["players"]:
                    try:
                        # 检查球员是否已存在
                        existing_players = self.player_service.get_players(team_name)
                        player_exists = any(
                            p.get("name") == player_info["name"] 
                            for p in existing_players
                        )
                        
                        if not player_exists:
                            # 添加新球员
                            self.player_service.add_player(
                                team_name,
                                player_info["name"],
                                player_info.get("jersey_number", ""),
                                None  # 照片稍后上传
                            )
                            sync_results["players_added"] += 1
                        else:
                            sync_results["players_updated"] += 1
                            
                    except Exception as e:
                        st.warning(f"添加球员 {player_info['name']} 失败: {e}")
            
            # 4. 创建换装就绪导出
            export_result = self.create_fashion_ready_export(team_name)
            sync_results["export_created"] = export_result["success"]
            
            return {
                "success": True,
                "sync_results": sync_results,
                "extracted_info": extracted_info,
                "export_result": export_result
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"同步聊天数据失败: {str(e)}"
            }
