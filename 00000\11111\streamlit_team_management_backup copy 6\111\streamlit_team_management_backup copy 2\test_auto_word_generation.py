#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI换装后自动Word生成功能
Test Auto Word Generation After AI Fashion Tryon

验证完整的自动化流程：AI换装 → 自动裁剪 → 自动Word生成
"""

import os
import sys
import tempfile
from PIL import Image

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

def create_test_image(width=400, height=600, color=(255, 0, 0)):
    """创建测试图片"""
    img = Image.new('RGB', (width, height), color)
    return img

def test_ai_image_engine_with_team_context():
    """测试AI图像引擎的球队上下文功能"""
    print("🧪 测试AI图像引擎的球队上下文功能...")
    
    try:
        from services.ai_image_engine import AIImageEngine
        from models.image_processing import ImageProcessingRequest, ProcessingType
        
        # 创建AI图像引擎
        engine = AIImageEngine()
        print("✅ AI图像引擎创建成功")
        
        # 创建测试图片
        test_img = create_test_image()
        temp_img_path = os.path.join(tempfile.gettempdir(), "test_player.jpg")
        test_img.save(temp_img_path, 'JPEG')
        print(f"✅ 测试图片创建成功: {temp_img_path}")
        
        # 准备球队上下文
        team_context = {
            'team_name': '测试足球队',
            'organization': '测试组织',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'current_player': {
                'name': '测试球员',
                'number': '10',
                'photo_path': temp_img_path
            },
            'other_players': []
        }
        
        # 创建处理请求（包含球队上下文）
        request = ImageProcessingRequest(
            source_image_path=temp_img_path,
            processing_types=[ProcessingType.ADD_WHITE_BACKGROUND],  # 使用简单的白底处理
            team_context=team_context
        )
        
        print("✅ 处理请求创建成功，包含球队上下文")
        print(f"   球队名称: {team_context['team_name']}")
        print(f"   当前球员: {team_context['current_player']['name']}")
        
        # 执行处理（这会触发自动Word生成）
        print("🎨 开始AI图像处理...")
        result = engine.process_image(request)
        
        if result.success:
            print("✅ AI图像处理成功!")
            print(f"   处理后图片: {result.processed_image_path}")
            print(f"   处理时间: {result.processing_time:.2f}秒")
            print("💡 如果配置正确，应该已自动生成Word报名表")
            return True
        else:
            print(f"❌ AI图像处理失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            if 'temp_img_path' in locals() and os.path.exists(temp_img_path):
                os.remove(temp_img_path)
        except:
            pass

def test_photo_service_with_auto_word():
    """测试照片服务的自动Word生成功能"""
    print("\n🧪 测试照片服务的自动Word生成功能...")
    
    try:
        from services.photo_service import PhotoService
        from models.processing_config import ProcessingConfig
        from models.player import Player
        
        # 创建照片服务
        photo_service = PhotoService()
        print("✅ 照片服务创建成功")
        
        # 创建测试球员
        test_player = Player(
            id="test_player_1",
            name="测试球员",
            jersey_number="10",
            photo="test_player.jpg"
        )
        
        # 创建测试图片
        test_img = create_test_image()
        temp_img_path = os.path.join(tempfile.gettempdir(), "test_player.jpg")
        test_img.save(temp_img_path, 'JPEG')
        
        # 创建处理配置
        config = ProcessingConfig.create_for_team("测试球队", None)
        config.add_player_config(test_player, "添加白底")
        
        # 修改球员配置的照片路径
        for player_config in config.processing_players:
            player_config.photo_path = temp_img_path
        
        print("✅ 处理配置创建成功")
        
        # 准备球队数据
        team_data = {
            'name': '测试足球队',
            'organization': '测试组织',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五'
        }
        
        # 执行处理（包含自动Word生成）
        print("🎨 开始照片处理（包含自动Word生成）...")
        result = photo_service.process_photos_with_auto_word(config, team_data)
        
        if result['success']:
            print("✅ 照片处理成功!")
            print(f"   成功处理: {result['successful_count']} 张照片")
            print(f"   处理时间: {result['actual_time']:.2f}秒")
            print("💡 应该已自动生成Word报名表")
            return True
        else:
            print(f"❌ 照片处理失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            if 'temp_img_path' in locals() and os.path.exists(temp_img_path):
                os.remove(temp_img_path)
        except:
            pass

def test_word_generator_service():
    """测试Word生成服务"""
    print("\n🧪 测试Word生成服务...")
    
    try:
        from word_generator_service import create_word_generator_service
        
        # 创建Word生成服务
        word_service = create_word_generator_service()
        print("✅ Word生成服务创建成功")
        
        # 准备测试数据
        team_data = {
            'name': '测试足球队',
            'organization': '测试组织',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五'
        }
        
        # 创建测试图片
        test_img = create_test_image()
        temp_img_path = os.path.join(tempfile.gettempdir(), "test_player_for_word.jpg")
        test_img.save(temp_img_path, 'JPEG')
        
        players_data = [
            {
                'name': '测试球员',
                'number': '10',
                'photo_path': temp_img_path
            }
        ]
        
        print("✅ 测试数据准备完成")
        
        # 生成Word报名表
        print("📄 开始生成Word报名表...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print("✅ Word报名表生成成功!")
            print(f"   文件路径: {result['file_path']}")
            print(f"   球队名称: {result.get('team_name')}")
            print(f"   球员数量: {result.get('player_count')}")
            
            # 检查文件是否存在
            if os.path.exists(result['file_path']):
                file_size = os.path.getsize(result['file_path'])
                print(f"   文件大小: {file_size} 字节")
                return True
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print(f"❌ Word报名表生成失败: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            if 'temp_img_path' in locals() and os.path.exists(temp_img_path):
                os.remove(temp_img_path)
        except:
            pass

def main():
    """主测试函数"""
    print("🚀 开始测试AI换装后自动Word生成功能")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("Word生成服务测试", test_word_generator_service),
        ("AI图像引擎球队上下文测试", test_ai_image_engine_with_team_context),
        ("照片服务自动Word生成测试", test_photo_service_with_auto_word)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 自动Word生成功能测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！AI换装后自动Word生成功能已成功实现！")
        print("\n💡 完整自动化流程:")
        print("   1. 用户上传球员照片")
        print("   2. AI进行换装处理")
        print("   3. Java自动裁剪照片为正方形")
        print("   4. 自动生成包含裁剪照片的Word报名表")
        print("   5. 用户获得完整的报名文档")
        
        print("\n🎯 实现的功能特点:")
        print("   ✅ 完全自动化 - 无需手动干预")
        print("   ✅ 智能触发 - AI换装后自动执行")
        print("   ✅ 先裁剪再生成Word - 符合您的设计")
        print("   ✅ 保持原有Java逻辑 - 复用成熟代码")
    else:
        print(f"\n⚠️ 还有 {total-passed} 个问题需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    main()
