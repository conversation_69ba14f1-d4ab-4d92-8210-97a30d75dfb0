#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word生成器UI组件
Word Generator UI Component

提供Word报名表生成的用户界面组件
"""

import streamlit as st
import os
from datetime import datetime
from pathlib import Path
from config.settings import app_settings
from word_generator_service import WordGeneratorService


class WordGeneratorComponent:
    """Word生成器UI组件类"""
    
    def __init__(self):
        """初始化组件"""
        self.word_service = None
        self._init_service()
    
    def _init_service(self):
        """初始化Word生成服务"""
        try:
            paths = app_settings.word_generator.get_absolute_paths()
            self.word_service = WordGeneratorService(
                jar_path=paths['jar_path'],
                template_path=paths['template_path'],
                output_dir=paths['output_dir']
            )
        except Exception as e:
            st.error(f"❌ Word生成服务初始化失败: {e}")
            self.word_service = None
    
    def render_word_generation_panel(self, team_data, players_data):
        """
        渲染Word生成面板
        
        Args:
            team_data: 球队数据
            players_data: 球员数据列表
        """
        st.markdown("---")
        st.markdown("### 📄 Word报名表生成")
        
        # 检查服务状态
        if not self.word_service:
            st.error("❌ Word生成服务不可用")
            with st.expander("🔧 配置说明"):
                self._render_setup_instructions()
            return
        
        # 检查数据
        if not self._validate_data_for_ui(team_data, players_data):
            return
        
        # 生成面板
        col1, col2 = st.columns([3, 1])
        
        with col1:
            self._render_info_panel(team_data, players_data)
        
        with col2:
            self._render_action_panel(team_data, players_data)
        
        # 文件管理面板
        self._render_file_management_panel()
    
    def _validate_data_for_ui(self, team_data, players_data):
        """验证数据并显示UI反馈"""
        if not team_data:
            st.warning("⚠️ 请先选择或创建球队")
            return False
        
        if not players_data:
            st.warning("⚠️ 当前球队没有球员数据")
            st.info("💡 请先添加球员信息")
            return False
        
        valid_players = [p for p in players_data if p.get('name') and p.get('jersey_number')]
        if len(valid_players) == 0:
            st.warning("⚠️ 没有有效的球员数据")
            st.info("💡 球员需要有姓名和球衣号码")
            return False
        
        return True
    
    def _render_info_panel(self, team_data, players_data):
        """渲染信息面板"""
        st.markdown("**📋 报名表信息预览**")
        
        # 球队信息
        team_name = team_data.get('name', '未命名球队')
        st.markdown(f"- **球队名称**: {team_name}")
        
        # 球员统计
        valid_players = [p for p in players_data if p.get('name') and p.get('jersey_number')]
        players_with_photos = [p for p in valid_players if p.get('photo')]
        
        st.markdown(f"- **球员总数**: {len(valid_players)}人")
        st.markdown(f"- **有照片球员**: {len(players_with_photos)}人")
        
        # 完成度
        completion_rate = (len(players_with_photos) / len(valid_players) * 100) if valid_players else 0
        st.markdown(f"- **照片完成度**: {completion_rate:.1f}%")
        
        # 进度条
        st.progress(completion_rate / 100)
        
        # 提示信息
        if completion_rate < 100:
            st.info("💡 建议上传所有球员照片以获得最佳报名表效果")
    
    def _render_action_panel(self, team_data, players_data):
        """渲染操作面板"""
        st.markdown("**🎯 生成操作**")
        
        # 生成按钮
        if st.button("📄 生成Word报名表", 
                    use_container_width=True, 
                    type="primary",
                    help="生成专业的Word格式报名表"):
            self._handle_word_generation(team_data, players_data)
        
        # 快速操作
        st.markdown("---")
        st.markdown("**⚡ 快速操作**")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("📁 打开输出目录", use_container_width=True):
                self._open_output_directory()
        
        with col2:
            if st.button("🗑️ 清理旧文件", use_container_width=True):
                self._cleanup_old_files()
    
    def _handle_word_generation(self, team_data, players_data):
        """处理Word生成"""
        # 创建进度容器
        progress_container = st.container()
        
        with progress_container:
            # 进度条
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            try:
                # 步骤1：准备数据
                status_text.text("🔧 准备数据...")
                progress_bar.progress(20)
                
                # 步骤2：生成报名表
                status_text.text("📝 正在生成Word报名表...")
                progress_bar.progress(60)
                
                result = self.word_service.generate_report(team_data, players_data)
                
                progress_bar.progress(100)
                
                if result['success']:
                    status_text.text("✅ Word报名表生成成功！")
                    
                    # 显示成功信息
                    st.success(f"✅ {result['message']}")
                    
                    # 文件信息
                    file_path = result['file_path']
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        st.info(f"📁 文件大小: {file_size / 1024:.1f} KB")
                        st.info(f"📍 保存位置: {file_path}")
                        
                        # 下载按钮
                        with open(file_path, 'rb') as f:
                            st.download_button(
                                label="📥 下载报名表",
                                data=f.read(),
                                file_name=os.path.basename(file_path),
                                mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                                use_container_width=True
                            )
                    else:
                        st.error("❌ 生成的文件不存在")
                else:
                    status_text.text("❌ Word报名表生成失败")
                    st.error(f"❌ {result['message']}")
                    if 'error' in result:
                        with st.expander("🔍 错误详情"):
                            st.code(result['error'])
                            
            except Exception as e:
                progress_bar.progress(0)
                status_text.text("❌ 生成过程出错")
                st.error(f"❌ 生成过程出错: {e}")
    
    def _render_file_management_panel(self):
        """渲染文件管理面板"""
        with st.expander("📁 文件管理", expanded=False):
            if self.word_service:
                files = self.word_service.get_output_files()
                
                if files:
                    st.markdown(f"**已生成的文件 ({len(files)}个)**")
                    
                    for i, file_info in enumerate(files[:10]):  # 只显示最新的10个
                        col1, col2, col3 = st.columns([3, 2, 1])
                        
                        with col1:
                            st.text(file_info['name'])
                        
                        with col2:
                            st.text(f"{file_info['size'] / 1024:.1f} KB")
                            st.text(file_info['modified'].strftime("%m-%d %H:%M"))
                        
                        with col3:
                            if os.path.exists(file_info['path']):
                                with open(file_info['path'], 'rb') as f:
                                    st.download_button(
                                        label="📥",
                                        data=f.read(),
                                        file_name=file_info['name'],
                                        mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                                        key=f"download_{i}"
                                    )
                else:
                    st.info("📝 还没有生成任何文件")
            else:
                st.error("❌ 文件管理服务不可用")
    
    def _render_setup_instructions(self):
        """渲染设置说明"""
        st.markdown("""
        **Word生成器配置说明：**
        
        1. **Java环境**: 确保已安装Java 8或更高版本
        2. **JAR文件**: 编译Java项目生成word-generator.jar
        3. **模板文件**: 准备Word模板文件template.docx
        4. **路径配置**: 检查config/settings.py中的路径配置
        
        **当前配置路径：**
        """)
        
        paths = app_settings.word_generator.get_absolute_paths()
        for key, path in paths.items():
            exists = "✅" if os.path.exists(path) else "❌"
            st.code(f"{key}: {path} {exists}")
    
    def _open_output_directory(self):
        """打开输出目录"""
        try:
            output_dir = app_settings.word_generator.get_absolute_paths()['output_dir']
            if os.path.exists(output_dir):
                os.startfile(output_dir)  # Windows
                st.success("📁 输出目录已打开")
            else:
                st.warning("📁 输出目录不存在")
        except Exception as e:
            st.error(f"❌ 无法打开目录: {e}")
    
    def _cleanup_old_files(self):
        """清理旧文件"""
        try:
            if self.word_service:
                self.word_service.cleanup_old_files(keep_count=10)
                st.success("🗑️ 旧文件清理完成")
                st.rerun()
            else:
                st.error("❌ 清理服务不可用")
        except Exception as e:
            st.error(f"❌ 清理失败: {e}")


def create_word_generator_component():
    """创建Word生成器组件实例"""
    return WordGeneratorComponent()
