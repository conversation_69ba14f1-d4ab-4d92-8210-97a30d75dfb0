# 球队管理系统 - 模块化版本

## 项目简介

这是一个基于Streamlit的球队管理系统，采用企业级模块化架构设计。系统提供球员信息管理、照片上传、AI照片处理、智能信息收集等功能。

## 功能特性

- 🏆 **球队管理**: 创建和管理多个球队
- 👥 **球员管理**: 添加、编辑、删除球员信息
- 📸 **照片管理**: 球员照片上传、压缩、存储
- 🎨 **AI照片处理**: 换装、背景去除、白底添加
- 🤖 **AI助手**: 智能收集球队报名信息
- 📤 **批量操作**: 批量上传球员照片和信息
- 📊 **数据导出**: 导出球队数据供后台处理

## 架构设计

### 模块化结构

```
streamlit_team_management_modular/
├── app.py                    # 主应用入口
├── requirements.txt          # 依赖文件
├── config/                   # 配置模块
│   ├── __init__.py
│   ├── settings.py          # 应用设置
│   └── constants.py         # 常量定义
├── models/                   # 数据模型
│   ├── __init__.py
│   ├── team.py             # 球队模型
│   ├── player.py           # 球员模型
│   └── processing_config.py # 处理配置模型
├── data/                     # 数据访问层
│   ├── __init__.py
│   ├── team_repository.py   # 球队数据访问
│   ├── player_repository.py # 球员数据访问
│   └── file_manager.py      # 文件管理
├── services/                 # 业务逻辑层
│   ├── __init__.py
│   ├── team_service.py      # 球队服务
│   ├── player_service.py    # 球员服务
│   ├── photo_service.py     # 照片服务
│   ├── ai_service.py        # AI服务
│   └── export_service.py    # 导出服务
├── components/               # UI组件层
│   ├── __init__.py
│   ├── sidebar.py           # 侧边栏
│   ├── player_form.py       # 球员表单
│   ├── batch_upload.py      # 批量上传
│   ├── photo_processing.py  # 照片处理
│   ├── ai_chat.py          # AI聊天
│   └── player_list.py       # 球员列表
└── utils/                    # 工具模块
    ├── __init__.py
    ├── image_utils.py       # 图片工具
    ├── validation.py        # 验证工具
    └── helpers.py           # 辅助函数
```

### 分层架构

1. **配置层 (config/)**: 集中管理应用配置和常量
2. **数据模型层 (models/)**: 定义数据结构和业务实体
3. **工具层 (utils/)**: 提供通用工具函数
4. **数据访问层 (data/)**: 处理数据持久化和访问
5. **业务逻辑层 (services/)**: 实现核心业务逻辑
6. **UI组件层 (components/)**: 可复用的UI组件
7. **应用层 (app.py)**: 应用入口和组件组装

## 安装和运行

### 环境要求

- Python 3.8+
- Streamlit 1.28.0+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd streamlit_team_management_modular
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置环境变量（可选）
```bash
# 如果需要使用AI功能，请配置OpenAI API密钥
export OPENAI_API_KEY="your-api-key"
```

4. 运行应用
```bash
streamlit run app.py
```

## 使用指南

### 基本操作

1. **创建球队**: 在侧边栏输入球队名称并点击"创建球队"
2. **添加球员**: 
   - 单个添加：点击"添加球员"按钮
   - 批量添加：点击"批量添加"按钮上传多张照片
3. **管理球员**: 在球员卡片中可以编辑或删除球员
4. **AI照片处理**: 点击侧边栏的"AI照片处理"进行照片处理
5. **数据导出**: 点击"导出数据"下载球队数据

### AI功能

- **智能信息收集**: AI助手帮助收集球队报名信息
- **照片处理**: 支持换装、背景去除、白底添加等功能

## 技术特性

### 代码质量

- ✅ **类型注解**: 所有函数都有完整的类型注解
- ✅ **文档字符串**: 详细的函数和类文档
- ✅ **错误处理**: 统一的异常处理机制
- ✅ **单一职责**: 每个模块职责明确
- ✅ **依赖注入**: 降低模块间耦合

### 设计原则

- **开闭原则**: 对扩展开放，对修改关闭
- **单一职责**: 每个类只有一个改变的理由
- **依赖倒置**: 高层模块不依赖低层模块
- **接口隔离**: 客户端不应依赖它不需要的接口

## 扩展开发

### 添加新功能

1. **数据模型**: 在 `models/` 中定义新的数据结构
2. **数据访问**: 在 `data/` 中实现数据访问逻辑
3. **业务逻辑**: 在 `services/` 中实现业务逻辑
4. **UI组件**: 在 `components/` 中创建UI组件
5. **集成**: 在 `app.py` 中集成新功能

### 配置管理

- 应用配置: 修改 `config/settings.py`
- 常量定义: 修改 `config/constants.py`

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题，请通过Issue联系我们。
