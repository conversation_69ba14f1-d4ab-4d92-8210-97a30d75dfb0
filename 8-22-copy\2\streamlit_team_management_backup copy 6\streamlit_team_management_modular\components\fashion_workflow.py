#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
换装工作流组件
Fashion Workflow Component

在AI聊天界面中集成自动换装功能
"""

import streamlit as st
import os
from typing import Dict, Any, Optional
from datetime import datetime

from services.fashion_workflow_service import FashionWorkflowService
from services.auth_service import AuthService


class FashionWorkflowComponent:
    """换装工作流组件"""
    
    def __init__(self):
        """初始化组件"""
        auth_service = AuthService()
        self.user_id = auth_service.get_current_user_id()
        self.workflow_service = FashionWorkflowService(self.user_id)
    
    def render_fashion_trigger(self, team_name: str) -> None:
        """
        渲染换装触发界面

        Args:
            team_name: 当前球队名称
        """
        st.markdown("#### 🎨 自动换装功能")

        # 检查统一场景的准备状态
        unified_status = self._check_unified_scenario(team_name)

        # 显示统一场景状态
        self._render_unified_status(unified_status)

        # 根据准备情况显示相应界面
        if unified_status["both_ready"]:
            self._render_fashion_options(team_name, unified_status)
        else:
            self._render_unified_guide(unified_status)
    
    def _render_readiness_status(self, readiness: Dict[str, Any]) -> None:
        """渲染准备状态"""
        if readiness["ready"]:
            st.success(f"✅ {readiness['message']}")
            
            # 显示详细信息
            with st.expander("📊 详细状态", expanded=False):
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("总球员数", readiness.get("total_players", 0))
                with col2:
                    st.metric("有照片球员", readiness.get("ready_players", 0))
                
                # 显示没有照片的球员
                players_without_photos = readiness.get("players_without_photos", [])
                if players_without_photos:
                    st.warning(f"⚠️ {len(players_without_photos)}名球员没有照片:")
                    for player in players_without_photos:
                        st.write(f"- {player.get('name', '未知')} (#{player.get('jersey_number', '?')})")
        else:
            reason = readiness.get("reason", "unknown")
            message = readiness.get("message", "未知错误")
            
            if reason == "no_ai_data":
                st.warning(f"⚠️ {message}")
                st.info("💡 请先使用AI助手收集球队信息")
            elif reason == "no_photos":
                st.warning(f"⚠️ {message}")
                st.info("💡 请先为球员上传照片")
            elif reason == "fashion_unavailable":
                st.error(f"❌ {message}")
                st.info("💡 请检查换装功能配置")
            else:
                st.error(f"❌ {message}")
    
    def _render_fashion_options(self, team_name: str, unified_status: Dict[str, Any]) -> None:
        """渲染换装选项"""
        st.markdown("##### 🚀 开始自动换装")

        # 显示统一场景状态
        st.info("🎯 统一换装模式：AI数据 + 球员照片")

        # 显示球员统计
        team_stats = unified_status["team_stats"]
        st.success(f"📊 准备就绪：{team_stats['players_with_photos']}/{team_stats['total_players']} 名球员有照片")

        # 显示AI数据状态
        if unified_status["ai_data"]:
            st.success("🤖 AI球队数据已收集完成")

        # 衣服图片选择
        clothes_option = st.radio(
            "选择球队服装:",
            ["使用默认服装", "上传自定义服装"],
            key=f"clothes_option_{team_name}"
        )

        clothes_image_path = None

        if clothes_option == "上传自定义服装":
            uploaded_clothes = st.file_uploader(
                "上传球队服装图片",
                type=['png', 'jpg', 'jpeg'],
                key=f"clothes_upload_{team_name}"
            )

            if uploaded_clothes:
                # 保存上传的衣服图片
                clothes_image_path = self._save_uploaded_clothes(uploaded_clothes, team_name)
                if clothes_image_path:
                    st.success("✅ 服装图片上传成功")
                    st.image(clothes_image_path, caption="球队服装", width=200)

        # 换装按钮
        col1, col2 = st.columns([1, 1])

        with col1:
            if st.button(
                "🎨 开始自动换装",
                type="primary",
                use_container_width=True,
                key=f"start_fashion_{team_name}"
            ):
                self._execute_unified_workflow(team_name, unified_status, clothes_image_path)

        with col2:
            if st.button(
                "📊 查看历史记录",
                use_container_width=True,
                key=f"view_history_{team_name}"
            ):
                self._show_workflow_history(team_name)
    
    def _render_preparation_guide(self, readiness: Dict[str, Any]) -> None:
        """渲染准备指南"""
        reason = readiness.get("reason", "unknown")
        
        st.markdown("##### 📋 准备步骤")
        
        if reason == "no_ai_data":
            st.markdown("""
            1. ✅ **使用AI助手收集球队信息**
               - 在上方AI聊天框中输入球队信息
               - 让AI助手帮您整理球员名单
            
            2. ⏳ **上传球员照片**
               - 使用批量上传功能
               - 或单个添加球员照片
            
            3. ⏳ **开始自动换装**
               - 信息收集完成后即可开始
            """)
        
        elif reason == "no_photos":
            st.markdown("""
            1. ✅ **AI信息收集完成**
            
            2. 🔄 **上传球员照片**
               - 点击"批量上传"按钮
               - 选择球员照片文件
               - 为每张照片分配球员信息
            
            3. ⏳ **开始自动换装**
               - 照片上传完成后即可开始
            """)
            
            # 显示快捷上传按钮
            if st.button("📤 快速上传照片", type="secondary"):
                st.session_state.batch_mode = 'upload'
                st.rerun()
    
    def _execute_fashion_workflow(self, team_name: str, clothes_image_path: Optional[str]) -> None:
        """执行换装工作流"""
        with st.spinner("🎨 正在执行自动换装..."):
            result = self.workflow_service.trigger_auto_fashion_workflow(
                team_name, clothes_image_path
            )
        
        if result["success"]:
            st.success("🎉 自动换装完成！")
            
            # 显示结果摘要
            workflow_result = result["workflow_result"]
            fashion_result = workflow_result["fashion_result"]
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("处理球员", fashion_result.get("successful_count", 0))
            with col2:
                st.metric("成功数量", fashion_result.get("successful_count", 0))
            with col3:
                st.metric("失败数量", fashion_result.get("failed_count", 0))
            
            # 显示详细结果
            with st.expander("📋 详细结果", expanded=True):
                for i, item in enumerate(fashion_result.get("results", []), 1):
                    player_image = item["player_image"]
                    result_data = item["result"]
                    
                    if result_data["success"]:
                        st.success(f"✅ 球员 {i}: 换装成功")
                        if "result_path" in result_data:
                            st.image(result_data["result_path"], caption=f"球员 {i} 换装结果", width=200)
                    else:
                        st.error(f"❌ 球员 {i}: {result_data.get('error', '换装失败')}")
        else:
            st.error(f"❌ 自动换装失败: {result['error']}")
    
    def _save_uploaded_clothes(self, uploaded_file, team_name: str) -> Optional[str]:
        """保存上传的衣服图片"""
        try:
            # 创建衣服图片目录
            clothes_dir = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "uploads",
                "team_clothes"
            )
            os.makedirs(clothes_dir, exist_ok=True)
            
            # 保存文件
            file_path = os.path.join(
                clothes_dir,
                f"{team_name}_clothes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{uploaded_file.name.split('.')[-1]}"
            )
            
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            return file_path
            
        except Exception as e:
            st.error(f"保存衣服图片失败: {e}")
            return None
    
    def _show_workflow_history(self, team_name: str) -> None:
        """显示工作流历史"""
        history = self.workflow_service.get_workflow_history(team_name)
        
        if not history:
            st.info("📭 暂无换装历史记录")
            return
        
        st.markdown("##### 📊 换装历史记录")
        
        for i, record in enumerate(history, 1):
            execution_time = record.get("execution_time", "未知时间")
            fashion_result = record.get("fashion_result", {})
            
            with st.expander(f"记录 {i}: {execution_time}", expanded=False):
                col1, col2 = st.columns(2)
                with col1:
                    st.write(f"**处理球员**: {fashion_result.get('total_processed', 0)}")
                    st.write(f"**成功数量**: {fashion_result.get('successful_count', 0)}")
                with col2:
                    st.write(f"**失败数量**: {fashion_result.get('failed_count', 0)}")
                    st.write(f"**总成本**: {fashion_result.get('total_cost_ptc', 0)} PTC")
    
    def should_show_fashion_trigger(self, team_name: str) -> bool:
        """
        判断是否应该显示换装触发器

        Args:
            team_name: 球队名称

        Returns:
            bool: 是否显示
        """
        # 统一场景：必须同时满足AI数据和照片两个条件

        # 条件1：AI收集球队数据
        readiness = self.workflow_service.check_fashion_readiness(team_name)
        has_ai_data = readiness.get("reason") != "no_ai_data"

        # 条件2：上传照片 + 球员信息
        team_stats = self.workflow_service.team_service.get_team_stats(team_name)
        has_photos_and_players = (
            team_stats.get('total_players', 0) > 0 and
            team_stats.get('players_with_photos', 0) > 0
        )

        # 两个条件都满足才显示换装功能
        both_ready = has_ai_data and has_photos_and_players

        # 如果有任一条件满足，就显示界面（用于引导用户完成另一条件）
        return has_ai_data or has_photos_and_players

    def _check_unified_scenario(self, team_name: str) -> Dict[str, Any]:
        """检查统一场景的准备状态"""
        # 条件1：AI收集球队数据
        readiness = self.workflow_service.check_fashion_readiness(team_name)
        has_ai_data = readiness.get("reason") != "no_ai_data"
        ai_data = readiness.get("ai_export_data")

        # 条件2：上传照片 + 球员信息
        team_stats = self.workflow_service.team_service.get_team_stats(team_name)
        has_photos_and_players = (
            team_stats.get('total_players', 0) > 0 and
            team_stats.get('players_with_photos', 0) > 0
        )

        # 两个条件都满足才能换装
        both_ready = has_ai_data and has_photos_and_players

        return {
            "has_ai_data": has_ai_data,
            "has_photos_and_players": has_photos_and_players,
            "both_ready": both_ready,
            "team_stats": team_stats,
            "ai_data": ai_data,
            "readiness": readiness
        }

    def _render_unified_status(self, status: Dict[str, Any]) -> None:
        """渲染统一场景状态"""
        st.markdown("##### 📋 换装准备状态")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**🤖 AI球队数据**")
            if status["has_ai_data"]:
                st.success("✅ 已收集")
                st.caption("AI已整理球队信息")
            else:
                st.error("❌ 未收集")
                st.caption("需要AI对话收集")

        with col2:
            st.markdown("**📸 球员照片**")
            if status["has_photos_and_players"]:
                team_stats = status["team_stats"]
                st.success("✅ 已上传")
                st.caption(f"{team_stats['players_with_photos']}/{team_stats['total_players']} 名球员")
            else:
                st.error("❌ 未上传")
                st.caption("需要上传照片和信息")

        # 显示整体状态
        if status["both_ready"]:
            st.success("🎉 **两个条件都已满足，可以开始换装！**")
        else:
            st.warning("⚠️ **需要同时满足AI数据和照片两个条件才能换装**")

    def _render_unified_guide(self, status: Dict[str, Any]) -> None:
        """渲染统一场景指南"""
        has_ai = status["has_ai_data"]
        has_photos = status["has_photos_and_players"]

        if has_ai and not has_photos:
            # 有AI数据，缺少照片
            self._render_need_photos_guide(status)
        elif not has_ai and has_photos:
            # 有照片，缺少AI数据
            self._render_need_ai_guide(status)
        elif not has_ai and not has_photos:
            # 两个都没有
            self._render_need_both_guide(status)

    def _render_need_photos_guide(self, status: Dict[str, Any]) -> None:
        """渲染需要照片的指南"""
        st.markdown("##### 📸 下一步：上传球员照片")

        st.success("✅ AI已收集球队数据")
        st.info("💡 现在需要上传球员照片并填写信息")

        st.markdown("""
        **操作步骤：**
        1. 点击下方按钮上传球员照片
        2. 为每张照片填写球员信息（姓名、号码）
        3. 选择处理方案（全套、去背景、白底等）
        4. 保存后即可开始换装
        """)

        col1, col2 = st.columns(2)
        with col1:
            if st.button("📤 批量上传照片", type="primary", key="need_photos_batch"):
                st.session_state.batch_mode = 'upload'
                st.rerun()
        with col2:
            if st.button("➕ 单个添加球员", key="need_photos_single"):
                st.session_state.show_add_form = True
                st.rerun()

    def _render_need_ai_guide(self, status: Dict[str, Any]) -> None:
        """渲染需要AI数据的指南"""
        st.markdown("##### 🤖 下一步：AI收集球队数据")

        st.success("✅ 球员照片已上传")
        team_stats = status["team_stats"]
        st.info(f"📊 当前有 {team_stats['players_with_photos']}/{team_stats['total_players']} 名球员有照片")

        st.warning("💡 现在需要在AI聊天中告诉AI您的球队信息")

        st.markdown("""
        **操作步骤：**
        1. 在上方AI聊天框中输入球队信息
        2. 告诉AI球队名称、球员名单、比赛信息等
        3. AI会自动整理和保存这些数据
        4. 完成后即可开始换装
        """)

        st.code('示例：我们是XX足球队，队员有张三、李四、王五...')

        st.info("💬 请滚动到页面上方的AI聊天区域开始对话")

    def _render_need_both_guide(self, status: Dict[str, Any]) -> None:
        """渲染需要两个条件的指南"""
        st.markdown("##### 🚀 开始使用换装功能")

        st.info("💡 换装功能需要同时满足两个条件")

        st.markdown("""
        **必需条件：**

        **1. 🤖 AI收集球队数据**
        - 在上方AI聊天中告诉AI您的球队信息
        - AI会自动整理球员名单、比赛信息等

        **2. 📸 上传球员照片**
        - 使用批量上传或单个添加功能
        - 为每张照片填写球员信息
        - 选择处理方案（全套、去背景、白底等）
        """)

        st.markdown("**推荐流程：**")

        tab1, tab2 = st.tabs(["先AI对话", "先上传照片"])

        with tab1:
            st.markdown("""
            1. 🤖 在上方AI聊天中输入球队信息
            2. 📸 然后上传球员照片
            3. 🎨 完成后开始换装
            """)
            st.info("💬 请滚动到页面上方开始AI对话")

        with tab2:
            st.markdown("""
            1. 📸 先上传球员照片和信息
            2. 🤖 然后在AI聊天中输入球队数据
            3. 🎨 完成后开始换装
            """)

            col1, col2 = st.columns(2)
            with col1:
                if st.button("📤 批量上传照片", type="primary", key="start_batch"):
                    st.session_state.batch_mode = 'upload'
                    st.rerun()
            with col2:
                if st.button("➕ 单个添加球员", key="start_single"):
                    st.session_state.show_add_form = True
                    st.rerun()

    def _check_both_scenarios(self, team_name: str) -> Dict[str, Any]:
        """检查两种场景的准备状态"""
        # 场景一：手动管理 - 需要球员数据和照片
        team_stats = self.workflow_service.team_service.get_team_stats(team_name)
        manual_scenario = {
            "total_players": team_stats.get('total_players', 0),
            "players_with_photos": team_stats.get('players_with_photos', 0),
            "ready": team_stats.get('total_players', 0) > 0 and team_stats.get('players_with_photos', 0) > 0
        }

        # 场景二：AI智能体验 - 需要AI数据和照片
        ai_readiness = self.workflow_service.check_fashion_readiness(team_name)
        ai_scenario = {
            "ready": ai_readiness.get("ready", False),  # 这个已经检查了AI数据+照片
            "reason": ai_readiness.get("reason", ""),
            "message": ai_readiness.get("message", ""),
            "ai_data": ai_readiness.get("ai_export_data"),
            "has_ai_data": ai_readiness.get("reason") != "no_ai_data",
            "has_photos": ai_readiness.get("ready_players", 0) > 0
        }

        return {
            "manual_scenario": manual_scenario,
            "ai_scenario": ai_scenario,
            "can_proceed": manual_scenario["ready"] or ai_scenario["ready"],
            "preferred_scenario": "ai" if ai_scenario["ready"] else ("manual" if manual_scenario["ready"] else "none")
        }

    def _render_scenario_status(self, scenario_status: Dict[str, Any]) -> None:
        """渲染场景状态"""
        manual = scenario_status["manual_scenario"]
        ai = scenario_status["ai_scenario"]

        # 显示两种场景的状态
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("##### 📋 手动管理模式")
            if manual["ready"]:
                st.success(f"✅ 准备就绪")
                st.caption(f"球员: {manual['players_with_photos']}/{manual['total_players']} 有照片")
            else:
                st.warning("⚠️ 未准备")
                if manual["total_players"] == 0:
                    st.caption("需要添加球员")
                else:
                    st.caption(f"需要上传照片 ({manual['players_with_photos']}/{manual['total_players']})")

        with col2:
            st.markdown("##### 🤖 AI智能模式")
            if ai["ready"]:
                st.success("✅ 准备就绪")
                st.caption("AI数据 ✅ + 照片 ✅")
            else:
                st.warning("⚠️ 未准备")
                # 显示具体缺少什么
                missing_parts = []
                if ai["reason"] == "no_ai_data":
                    missing_parts.append("AI数据 ❌")
                else:
                    missing_parts.append("AI数据 ✅")

                if ai["reason"] == "no_photos" or not ai.get("has_photos", False):
                    missing_parts.append("照片 ❌")
                else:
                    missing_parts.append("照片 ✅")

                st.caption(" + ".join(missing_parts))

                # 显示具体提示
                if ai["reason"] == "no_ai_data":
                    st.caption("💡 需要AI收集信息")
                elif ai["reason"] == "no_photos":
                    st.caption("💡 需要上传照片")
                else:
                    st.caption(ai.get("message", "未知状态"))

    def _render_dual_scenario_guide(self, scenario_status: Dict[str, Any]) -> None:
        """渲染双场景指南"""
        st.markdown("##### 🚀 选择您的使用方式")

        tab1, tab2 = st.tabs(["📋 手动管理", "🤖 AI智能体验"])

        with tab1:
            st.markdown("""
            **适合场景：** 您已经有明确的球员名单和照片

            **操作步骤：**
            1. 点击"批量添加"或"添加球员"
            2. 上传球员照片并填写信息
            3. 选择处理方案（全套、去背景、白底等）
            4. 保存球员信息
            5. 返回此处开始换装
            """)

            manual = scenario_status["manual_scenario"]
            if manual["total_players"] == 0:
                if st.button("➕ 开始添加球员", type="primary", key="start_manual"):
                    st.session_state.show_add_form = True
                    st.rerun()
            elif manual["players_with_photos"] == 0:
                st.info("💡 您已有球员信息，现在需要上传照片")
                if st.button("📤 批量上传照片", type="primary", key="upload_photos"):
                    st.session_state.batch_mode = 'upload'
                    st.rerun()
            else:
                st.success("✅ 手动管理模式已准备就绪！")

        with tab2:
            st.markdown("""
            **适合场景：** 您希望通过对话快速收集球队信息

            **操作步骤：**
            1. 在上方AI聊天中告诉AI您的球队信息
            2. AI会自动整理球员名单和相关信息
            3. 上传球员照片
            4. **两个条件都满足后**，AI自动检测并提示换装
            """)

            ai = scenario_status["ai_scenario"]

            # 显示当前状态
            st.markdown("**当前状态：**")
            col1, col2 = st.columns(2)
            with col1:
                if ai.get("has_ai_data", False):
                    st.success("✅ AI数据已收集")
                else:
                    st.error("❌ 需要AI收集数据")
            with col2:
                if ai.get("has_photos", False):
                    st.success("✅ 照片已上传")
                else:
                    st.error("❌ 需要上传照片")

            # 根据状态提供操作建议
            if ai["reason"] == "no_ai_data":
                st.info("💡 第一步：请在上方AI聊天中输入球队信息")
                st.code('"我们是XX足球队，队员有张三、李四、王五..."')
                st.warning("⚠️ 完成AI数据收集后，还需要上传照片才能换装")
            elif ai["reason"] == "no_photos":
                st.info("💡 第二步：AI已收集信息，现在需要上传照片")
                if st.button("📤 快速上传照片", type="primary", key="ai_upload_photos"):
                    st.session_state.batch_mode = 'upload'
                    st.rerun()
                st.success("✅ 上传照片后即可自动换装！")
            elif ai["ready"]:
                st.success("🎉 AI智能模式完全准备就绪！")
                st.info("🎨 AI数据 + 照片都已完成，可以开始换装了")
            else:
                st.warning("⚠️ 需要完成AI数据收集和照片上传两个步骤")

    def _execute_dual_scenario_workflow(self, team_name: str, scenario_status: Dict[str, Any], clothes_image_path: Optional[str]) -> None:
        """执行双场景换装工作流"""
        preferred = scenario_status["preferred_scenario"]

        if preferred == "ai":
            # 使用AI场景的工作流
            self._execute_ai_scenario_workflow(team_name, scenario_status, clothes_image_path)
        elif preferred == "manual":
            # 使用手动场景的工作流
            self._execute_manual_scenario_workflow(team_name, scenario_status, clothes_image_path)
        else:
            st.error("❌ 无法确定使用哪种场景，请检查数据准备情况")

    def _execute_ai_scenario_workflow(self, team_name: str, scenario_status: Dict[str, Any], clothes_image_path: Optional[str]) -> None:
        """执行AI场景工作流"""
        st.info("🤖 使用AI智能模式执行换装...")

        # 使用原有的AI工作流
        with st.spinner("🎨 正在执行AI智能换装..."):
            result = self.workflow_service.trigger_auto_fashion_workflow(
                team_name, clothes_image_path
            )

        if result["success"]:
            st.success("🎉 AI智能换装完成！")
            self._show_workflow_result(result["workflow_result"])
        else:
            st.error(f"❌ AI换装失败: {result['error']}")

    def _execute_manual_scenario_workflow(self, team_name: str, scenario_status: Dict[str, Any], clothes_image_path: Optional[str]) -> None:
        """执行手动场景工作流"""
        st.info("📋 使用手动管理模式执行换装...")

        manual = scenario_status["manual_scenario"]

        # 获取手动添加的球员数据
        players = self.workflow_service.player_service.get_players(team_name)
        players_with_photos = [p for p in players if p.photo_path and os.path.exists(p.photo_path)]

        if not players_with_photos:
            st.error("❌ 没有找到有照片的球员")
            return

        # 准备换装数据
        player_images = [p.photo_path for p in players_with_photos]

        # 使用默认衣服图片（如果没有提供）
        if not clothes_image_path:
            clothes_image_path = self.workflow_service._get_default_clothes_image()

        if not clothes_image_path or not os.path.exists(clothes_image_path):
            st.error("❌ 未找到衣服图片，请先上传球队服装图片")
            return

        # 执行批量换装
        with st.spinner("🎨 正在执行手动模式换装..."):
            try:
                fashion_result = self.workflow_service.ai_service.fashion_tryon_batch(
                    player_images, clothes_image_path
                )

                # 保存工作流结果
                workflow_result = {
                    "team_name": team_name,
                    "execution_time": datetime.now().isoformat(),
                    "scenario": "manual",
                    "fashion_result": fashion_result,
                    "clothes_image_path": clothes_image_path,
                    "processed_players": len(player_images)
                }

                self.workflow_service._save_workflow_result(team_name, workflow_result)

                st.success("🎉 手动模式换装完成！")
                self._show_workflow_result(workflow_result)

            except Exception as e:
                st.error(f"❌ 手动换装失败: {str(e)}")

    def _show_workflow_result(self, workflow_result: Dict[str, Any]) -> None:
        """显示工作流结果"""
        fashion_result = workflow_result.get("fashion_result", {})

        # 显示结果摘要
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("处理球员", workflow_result.get("processed_players", 0))
        with col2:
            st.metric("成功数量", fashion_result.get("successful_count", 0))
        with col3:
            st.metric("失败数量", fashion_result.get("failed_count", 0))

        # 显示详细结果
        if fashion_result.get("results"):
            with st.expander("📊 详细结果", expanded=False):
                for i, result in enumerate(fashion_result["results"], 1):
                    st.write(f"**球员 {i}:**")
                    if result.get("success"):
                        st.success(f"✅ 换装成功")
                        if result.get("output_url"):
                            st.image(result["output_url"], caption=f"球员 {i} 换装结果", width=200)
                    else:
                        st.error(f"❌ 换装失败: {result.get('error', '未知错误')}")

        # 显示成本信息
        if fashion_result.get("total_cost_ptc"):
            st.info(f"💰 总成本: {fashion_result['total_cost_ptc']} PTC")

    def _execute_unified_workflow(self, team_name: str, unified_status: Dict[str, Any], clothes_image_path: Optional[str]) -> None:
        """执行统一换装工作流"""
        st.info("🎯 使用统一模式执行换装（AI数据 + 球员照片）...")

        # 检查是否有AI导出数据可用
        readiness = unified_status["readiness"]
        if readiness.get("ready", False):
            # 使用AI导出数据的工作流
            self._execute_ai_based_workflow(team_name, readiness, clothes_image_path)
        else:
            # 使用手动数据的工作流
            self._execute_manual_based_workflow(team_name, unified_status, clothes_image_path)

    def _execute_ai_based_workflow(self, team_name: str, readiness: Dict[str, Any], clothes_image_path: Optional[str]) -> None:
        """基于AI数据执行工作流"""
        with st.spinner("🎨 正在执行AI数据驱动的换装..."):
            result = self.workflow_service.trigger_auto_fashion_workflow(
                team_name, clothes_image_path
            )

        if result["success"]:
            st.success("🎉 AI数据驱动换装完成！")
            self._show_workflow_result(result["workflow_result"])
        else:
            st.error(f"❌ AI换装失败: {result['error']}")

    def _execute_manual_based_workflow(self, team_name: str, unified_status: Dict[str, Any], clothes_image_path: Optional[str]) -> None:
        """基于手动数据执行工作流"""
        st.info("📋 使用手动数据 + AI球队信息执行换装...")

        # 获取手动添加的球员数据
        players = self.workflow_service.player_service.get_players(team_name)
        players_with_photos = [p for p in players if p.photo_path and os.path.exists(p.photo_path)]

        if not players_with_photos:
            st.error("❌ 没有找到有照片的球员")
            return

        # 准备换装数据
        player_images = [p.photo_path for p in players_with_photos]

        # 使用默认衣服图片（如果没有提供）
        if not clothes_image_path:
            clothes_image_path = self.workflow_service._get_default_clothes_image()

        if not clothes_image_path or not os.path.exists(clothes_image_path):
            st.error("❌ 未找到衣服图片，请先上传球队服装图片")
            return

        # 执行批量换装
        with st.spinner("🎨 正在执行统一模式换装..."):
            try:
                fashion_result = self.workflow_service.ai_service.fashion_tryon_batch(
                    player_images, clothes_image_path
                )

                # 保存工作流结果
                workflow_result = {
                    "team_name": team_name,
                    "execution_time": datetime.now().isoformat(),
                    "scenario": "unified",
                    "fashion_result": fashion_result,
                    "clothes_image_path": clothes_image_path,
                    "processed_players": len(player_images),
                    "has_ai_data": unified_status["has_ai_data"],
                    "has_photos": unified_status["has_photos_and_players"]
                }

                self.workflow_service._save_workflow_result(team_name, workflow_result)

                st.success("🎉 统一模式换装完成！")
                self._show_workflow_result(workflow_result)

            except Exception as e:
                st.error(f"❌ 统一模式换装失败: {str(e)}")
