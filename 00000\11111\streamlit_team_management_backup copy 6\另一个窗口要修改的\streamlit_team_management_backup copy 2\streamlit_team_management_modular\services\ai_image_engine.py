#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI图片处理引擎
AI Image Processing Engine

共享的底层AI图片处理引擎，支持球队管理和独立修图两种场景
"""

import os
import time
from typing import Optional, List, Dict, Any
import streamlit as st
from PIL import Image

from models.image_processing import ImageProcessingRequest, ProcessingResult, ProcessingType
from utils.image_utils import ImageProcessor
from config.settings import app_settings


class AIImageEngine:
    """AI图片处理引擎"""
    
    def __init__(self):
        self.temp_folder = os.path.join(app_settings.paths.PROCESSED_PHOTOS_FOLDER, "temp")
        self.ensure_temp_folder()
    
    def ensure_temp_folder(self) -> None:
        """确保临时文件夹存在"""
        if not os.path.exists(self.temp_folder):
            os.makedirs(self.temp_folder)
    
    def process_image(self, request: ImageProcessingRequest) -> ProcessingResult:
        """
        处理图片
        
        Args:
            request: 处理请求
            
        Returns:
            ProcessingResult: 处理结果
        """
        start_time = time.time()
        
        try:
            # 验证输入
            if not os.path.exists(request.source_image_path):
                return ProcessingResult(
                    request_id=request.id,
                    success=False,
                    error_message="源图片文件不存在"
                )
            
            # 检查是否需要模板图
            needs_template = any(pt == ProcessingType.FASHION_TRYON for pt in request.processing_types)
            if needs_template and not request.template_image_path:
                return ProcessingResult(
                    request_id=request.id,
                    success=False,
                    error_message="换装处理需要模板图"
                )
            
            # 生成输出文件路径
            output_filename = f"processed_{request.id}.jpg"
            output_path = os.path.join(self.temp_folder, output_filename)
            
            # 执行处理
            success = self._execute_processing(request, output_path)
            
            processing_time = time.time() - start_time
            
            if success:
                result = ProcessingResult(
                    request_id=request.id,
                    success=True,
                    processed_image_path=output_path,
                    processing_time=processing_time
                )

                # 🎯 AI换装完成后自动触发Word生成
                self._trigger_auto_word_generation(request, output_path)

                return result
            else:
                return ProcessingResult(
                    request_id=request.id,
                    success=False,
                    error_message="图片处理失败",
                    processing_time=processing_time
                )
                
        except Exception as e:
            processing_time = time.time() - start_time
            return ProcessingResult(
                request_id=request.id,
                success=False,
                error_message=f"处理异常: {str(e)}",
                processing_time=processing_time
            )
    
    def _execute_processing(self, request: ImageProcessingRequest, output_path: str) -> bool:
        """
        执行具体的处理逻辑
        
        Args:
            request: 处理请求
            output_path: 输出路径
            
        Returns:
            bool: 是否成功
        """
        try:
            # 打开源图片
            with Image.open(request.source_image_path) as img:
                processed_img = img.copy()
                
                # 按顺序执行处理步骤
                for processing_type in request.processing_types:
                    if processing_type == ProcessingType.FASHION_TRYON:
                        processed_img = self._apply_fashion_tryon(processed_img, request.template_image_path)
                    elif processing_type == ProcessingType.REMOVE_BACKGROUND:
                        processed_img = self._remove_background(processed_img)
                    elif processing_type == ProcessingType.ADD_WHITE_BACKGROUND:
                        processed_img = self._add_white_background(processed_img)
                
                # 保存处理后的图片
                if processed_img.mode in ('RGBA', 'P'):
                    processed_img = processed_img.convert('RGB')
                
                processed_img.save(output_path, 'JPEG', quality=85, optimize=True)
                return True
                
        except Exception as e:
            st.error(f"图片处理执行失败: {e}")
            return False
    
    def _apply_fashion_tryon(self, img: Image.Image, template_path: str) -> Image.Image:
        """
        应用换装处理（模拟实现）
        
        Args:
            img: 源图片
            template_path: 模板图片路径
            
        Returns:
            Image.Image: 处理后的图片
        """
        # 这里是模拟实现，实际应该调用AI换装API
        st.info("🎨 正在进行AI换装处理...")
        
        # 模拟处理时间
        time.sleep(1)
        
        # 实际实现中，这里应该：
        # 1. 调用AI换装API
        # 2. 传入源图片和模板图片
        # 3. 返回换装后的图片
        
        return img
    
    def _remove_background(self, img: Image.Image) -> Image.Image:
        """
        移除背景（模拟实现）
        
        Args:
            img: 源图片
            
        Returns:
            Image.Image: 处理后的图片
        """
        # 这里是模拟实现，实际应该调用背景去除API
        st.info("🖼️ 正在进行背景去除处理...")
        
        # 模拟处理时间
        time.sleep(1)
        
        # 实际实现中，这里应该：
        # 1. 调用背景去除API（如remove.bg）
        # 2. 返回去除背景后的图片
        
        return img
    
    def _add_white_background(self, img: Image.Image) -> Image.Image:
        """
        添加白色背景
        
        Args:
            img: 源图片
            
        Returns:
            Image.Image: 处理后的图片
        """
        st.info("⚪ 正在添加白色背景...")
        
        # 创建白色背景
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # 创建白色背景图片
        white_bg = Image.new('RGBA', img.size, (255, 255, 255, 255))
        
        # 将原图片合成到白色背景上
        result = Image.alpha_composite(white_bg, img)
        
        return result.convert('RGB')
    
    def cleanup_temp_files(self, older_than_hours: int = 24) -> None:
        """
        清理临时文件
        
        Args:
            older_than_hours: 清理多少小时前的文件
        """
        try:
            current_time = time.time()
            cutoff_time = current_time - (older_than_hours * 3600)
            
            for filename in os.listdir(self.temp_folder):
                file_path = os.path.join(self.temp_folder, filename)
                if os.path.isfile(file_path):
                    file_time = os.path.getmtime(file_path)
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        
        except Exception as e:
            st.warning(f"清理临时文件失败: {e}")
    
    def get_processing_options(self) -> Dict[str, Dict[str, Any]]:
        """
        获取处理选项配置
        
        Returns:
            Dict[str, Dict[str, Any]]: 处理选项配置
        """
        return {
            "换装": {
                "label": "🔄 AI换装",
                "description": "使用AI技术将人物服装替换为模板样式",
                "processing_type": ProcessingType.FASHION_TRYON,
                "needs_template": True
            },
            "背景去除": {
                "label": "🖼️ 背景去除",
                "description": "智能识别并移除图片背景",
                "processing_type": ProcessingType.REMOVE_BACKGROUND,
                "needs_template": False
            },
            "添加白底": {
                "label": "⚪ 添加白底",
                "description": "为图片添加纯白色背景",
                "processing_type": ProcessingType.ADD_WHITE_BACKGROUND,
                "needs_template": False
            }
        }

    def _trigger_auto_word_generation(self, request: ImageProcessingRequest, processed_image_path: str) -> None:
        """
        AI换装完成后自动触发Word生成

        Args:
            request: 处理请求
            processed_image_path: 处理后的图片路径
        """
        try:
            # 检查是否包含换装处理
            if ProcessingType.FASHION_TRYON not in request.processing_types:
                return

            # 检查是否有球队上下文信息
            if not hasattr(request, 'team_context') or not request.team_context:
                st.info("💡 AI换装完成！如需生成Word报名表，请确保已保存球队信息。")
                return

            st.success("🎨 AI换装完成！正在自动生成Word报名表...")

            # 导入Word生成服务
            from word_generator_service import create_word_generator_service

            # 创建Word生成服务
            word_service = create_word_generator_service()

            # 准备球队数据
            team_data = self._prepare_team_data_for_word(request.team_context)

            # 准备球员数据（使用处理后的照片路径）
            players_data = self._prepare_players_data_for_word(request.team_context, processed_image_path)

            # 生成Word报名表
            result = word_service.generate_report(team_data, players_data)

            if result['success']:
                st.success(f"✅ Word报名表自动生成成功！")
                st.success(f"📁 文件路径: {result['file_path']}")

                # 提供下载按钮
                if os.path.exists(result['file_path']):
                    with open(result['file_path'], 'rb') as f:
                        st.download_button(
                            label="📥 下载Word报名表",
                            data=f.read(),
                            file_name=f"{team_data.get('name', '球队')}_报名表.docx",
                            mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                            key=f"download_word_{request.id}"
                        )
            else:
                st.error(f"❌ Word报名表生成失败: {result.get('message', '未知错误')}")

        except Exception as e:
            st.warning(f"⚠️ 自动Word生成失败: {e}")
            st.info("💡 您可以手动在Word生成页面生成报名表")

    def _prepare_team_data_for_word(self, team_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        为Word生成准备球队数据

        Args:
            team_context: 球队上下文信息

        Returns:
            Dict[str, Any]: Word生成所需的球队数据
        """
        return {
            'name': team_context.get('team_name', '未知球队'),
            'organization': team_context.get('organization', ''),
            'leader': team_context.get('leader', ''),
            'coach': team_context.get('coach', ''),
            'doctor': team_context.get('doctor', ''),
            'contact': team_context.get('contact', '')
        }

    def _prepare_players_data_for_word(self, team_context: Dict[str, Any], processed_image_path: str) -> List[Dict[str, Any]]:
        """
        为Word生成准备球员数据

        Args:
            team_context: 球队上下文信息
            processed_image_path: 处理后的图片路径

        Returns:
            List[Dict[str, Any]]: Word生成所需的球员数据
        """
        players_data = []

        # 获取当前处理的球员信息
        current_player = team_context.get('current_player', {})

        if current_player:
            players_data.append({
                'name': current_player.get('name', ''),
                'jersey_number': current_player.get('number', ''),
                'photo': processed_image_path  # 使用AI处理后的照片
            })

        # 获取其他球员信息
        other_players = team_context.get('other_players', [])
        for player in other_players:
            players_data.append({
                'name': player.get('name', ''),
                'jersey_number': player.get('number', ''),
                'photo': player.get('photo_path', '')  # 使用原始照片路径
            })

        return players_data
