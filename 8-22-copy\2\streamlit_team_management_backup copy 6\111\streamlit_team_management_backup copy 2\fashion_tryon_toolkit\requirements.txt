# 时尚换装工具包依赖 (Fashion Try-On Toolkit Dependencies)

# 核心依赖 (Core Dependencies)
requests>=2.28.0          # HTTP请求库，用于API调用
Pillow>=9.0.0            # 图片处理库，用于图片操作和格式转换
numpy>=1.21.0            # 数值计算库，支持图片数组操作

# 异步依赖 (Async Dependencies)
aiohttp>=3.8.0           # 异步HTTP客户端库
aiofiles>=22.1.0         # 异步文件操作库

# 可选依赖 (Optional Dependencies)
matplotlib>=3.5.0        # 绘图库，用于结果分析和可视化
pathlib                  # 路径处理库 (Python 3.4+ 内置)

# 开发和测试依赖 (Development and Testing Dependencies)
# pytest>=7.0.0          # 测试框架 (如需运行测试)
# black>=22.0.0           # 代码格式化工具 (如需代码格式化)
# flake8>=4.0.0           # 代码检查工具 (如需代码检查)

# 注意事项 (Notes):
# 1. Python 版本要求: >= 3.7
# 2. 所有依赖都是常用库，安装简单
# 3. 如果只使用核心功能，只需安装前3个依赖
# 4. matplotlib 仅在需要生成分析图表时使用
