/**
 * 球员数据模型 - Word生成核心模块
 * 专用于Word报名表生成，移除了AI相关功能
 */
public class PlayerData {
    private String number;      // 球衣号码
    private String name;        // 球员姓名
    private String photoPath;   // 照片路径

    public PlayerData() {}

    public PlayerData(String number, String name, String photoPath) {
        this.number = number;
        this.name = name;
        this.photoPath = photoPath;
    }

    /**
     * 从Python数据创建PlayerData对象
     * 兼容Python字典格式
     */
    public static PlayerData fromPythonData(String number, String name, String photoPath) {
        return new PlayerData(number, name, photoPath);
    }

    // Getters and Setters
    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhotoPath() {
        return photoPath;
    }

    public void setPhotoPath(String photoPath) {
        this.photoPath = photoPath;
    }

    /**
     * 验证球员数据是否完整
     */
    public boolean isValid() {
        return number != null && !number.trim().isEmpty() &&
               name != null && !name.trim().isEmpty();
    }

    @Override
    public String toString() {
        return String.format("球员[号码=%s, 姓名=%s, 照片=%s]", number, name, photoPath);
    }
}

/**
 * 队伍基本信息模型 - Word生成核心模块
 * 专用于Word报名表生成
 */
class TeamInfo {
    private String title;           // 比赛标题
    private String organizationName; // 单位名称
    private String teamLeader;      // 领队
    private String coach;           // 教练
    private String teamDoctor;      // 队医

    public TeamInfo() {}

    /**
     * 从Python数据创建TeamInfo对象
     */
    public static TeamInfo fromPythonData(String title, String organizationName,
                                        String teamLeader, String coach, String teamDoctor) {
        TeamInfo teamInfo = new TeamInfo();
        teamInfo.setTitle(title);
        teamInfo.setOrganizationName(organizationName);
        teamInfo.setTeamLeader(teamLeader);
        teamInfo.setCoach(coach);
        teamInfo.setTeamDoctor(teamDoctor);
        return teamInfo;
    }

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getTeamLeader() {
        return teamLeader;
    }

    public void setTeamLeader(String teamLeader) {
        this.teamLeader = teamLeader;
    }

    public String getCoach() {
        return coach;
    }

    public void setCoach(String coach) {
        this.coach = coach;
    }

    public String getTeamDoctor() {
        return teamDoctor;
    }

    public void setTeamDoctor(String teamDoctor) {
        this.teamDoctor = teamDoctor;
    }

    /**
     * 验证队伍信息是否完整
     */
    public boolean isValid() {
        return title != null && !title.trim().isEmpty() &&
               organizationName != null && !organizationName.trim().isEmpty();
    }

    @Override
    public String toString() {
        return String.format("队伍信息[标题=%s, 单位=%s, 领队=%s, 教练=%s, 队医=%s]",
                           title, organizationName, teamLeader, coach, teamDoctor);
    }
}

/**
 * 完整的足球队数据模型 - Word生成核心模块
 * 专用于Word报名表生成，优化了Python集成
 */
class FootballTeamData {
    private TeamInfo teamInfo;
    private PlayerData[] players;

    public FootballTeamData() {
        this.players = new PlayerData[10]; // 支持10个球员
    }

    /**
     * 从Python数据创建FootballTeamData对象
     * 兼容Python足球系统的数据格式
     */
    public static FootballTeamData fromPythonData(TeamInfo teamInfo, PlayerData[] players) {
        FootballTeamData teamData = new FootballTeamData();
        teamData.setTeamInfo(teamInfo);
        teamData.setPlayers(players);
        return teamData;
    }

    public TeamInfo getTeamInfo() {
        return teamInfo;
    }

    public void setTeamInfo(TeamInfo teamInfo) {
        this.teamInfo = teamInfo;
    }

    public PlayerData[] getPlayers() {
        return players;
    }

    public void setPlayers(PlayerData[] players) {
        this.players = players;
    }

    public void setPlayer(int index, PlayerData player) {
        if (index >= 0 && index < players.length) {
            players[index] = player;
        }
    }

    public PlayerData getPlayer(int index) {
        if (index >= 0 && index < players.length) {
            return players[index];
        }
        return null;
    }

    public int getPlayerCount() {
        int count = 0;
        for (PlayerData player : players) {
            if (player != null && player.isValid()) {
                count++;
            }
        }
        return count;
    }

    /**
     * 验证整个队伍数据是否有效
     */
    public boolean isValid() {
        return teamInfo != null && teamInfo.isValid() && getPlayerCount() > 0;
    }

    /**
     * 获取有效球员列表（用于调试）
     */
    public String getValidPlayersInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("有效球员数量: ").append(getPlayerCount()).append("\n");
        for (int i = 0; i < players.length; i++) {
            if (players[i] != null && players[i].isValid()) {
                sb.append("  ").append(i + 1).append(". ").append(players[i].toString()).append("\n");
            }
        }
        return sb.toString();
    }
}
