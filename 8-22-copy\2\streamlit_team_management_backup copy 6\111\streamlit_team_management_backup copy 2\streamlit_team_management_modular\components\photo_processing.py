#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片处理组件
Photo Processing Component

提供照片处理相关的UI组件
"""

import streamlit as st
from typing import List, Dict, Any

from models.player import Player
from services.photo_service import PhotoService
from config.constants import ProcessOptions, UIConstants


class PhotoProcessingComponent:
    """照片处理组件"""

    def __init__(self):
        # 获取当前用户ID用于PhotoService
        user_id = self._get_current_user_id()
        self.photo_service = PhotoService(user_id)

    def _get_current_user_id(self) -> str:
        """获取当前用户ID"""
        return st.session_state.get('user_id', '')
    
    def render_processing_interface(self, team_name: str, players: List[Player]) -> None:
        """
        渲染照片处理界面
        
        Args:
            team_name: 球队名称
            players: 球员列表
        """
        st.subheader("🎨 AI照片处理")
        st.markdown("为球员照片进行个性化AI处理：每个球员可选择不同的处理方案")
        
        if not players:
            st.warning("当前球队没有球员，请先添加球员")
            if st.button("❌ 返回"):
                st.session_state.batch_mode = 'normal'
                st.rerun()
            return
        
        # 模板图上传
        clothes_image = self.render_template_upload()
        
        # 批量设置
        self.render_batch_settings(players)
        
        # 个性化球员处理配置
        self.render_player_processing_config(team_name, players)
        
        # 处理统计和执行
        self.render_processing_execution(team_name, players, clothes_image)
    
    def render_template_upload(self) -> Any:
        """
        渲染模板图上传区域
        
        Returns:
            Any: 上传的模板文件
        """
        st.markdown("### 上传模板图")
        st.markdown("*如果需要换装处理，请上传模板图。模板图是用于AI换装的参考图片，可以是球衣、队服或其他服装样式图片*")
        
        clothes_image = st.file_uploader(
            "选择模板图片（换装时需要）",
            type=['png', 'jpg', 'jpeg'],
            help=UIConstants.HELP_TEXTS["template_upload"]
        )
        
        # 添加英文界面说明
        st.markdown(
            f"""
            <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-top: 10px;">
                <small>{UIConstants.HELP_TEXTS["english_ui"]}</small>
            </div>
            """, 
            unsafe_allow_html=True
        )
        
        if clothes_image:
            col1, col2 = st.columns([1, 2])
            with col1:
                st.image(clothes_image, caption="模板图预览", width=200)
            with col2:
                st.info("✅ 模板图已选择")
                st.markdown("**模板图说明：**")
                st.markdown("- 🎽 **球衣模板**：用于统一球员球衣样式")
                st.markdown("- 👕 **服装模板**：用于更换球员服装风格")
                st.markdown("- 🎨 **设计模板**：用于展示不同设计效果")
        
        return clothes_image
    
    def render_batch_settings(self, players: List[Player]) -> None:
        """
        渲染批量设置区域
        
        Args:
            players: 球员列表
        """
        st.markdown("### 批量设置")
        st.markdown("*快速为所有球员设置相同的处理方案，然后可以单独调整特殊情况*")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("🔄🖼️⚪ 全部设为全套处理"):
                for player in players:
                    st.session_state[f"process_option_{player.id}"] = "全套处理"
                st.rerun()
        
        with col2:
            if st.button("🖼️⚪ 全部设为背景去除+白底"):
                for player in players:
                    st.session_state[f"process_option_{player.id}"] = "背景去除+白底"
                st.rerun()
        
        with col3:
            if st.button("⚪ 全部设为仅白底"):
                for player in players:
                    st.session_state[f"process_option_{player.id}"] = "仅白底"
                st.rerun()
        
        with col4:
            if st.button("🚫 全部设为不处理"):
                for player in players:
                    st.session_state[f"process_option_{player.id}"] = "不处理"
                st.rerun()
    
    def render_player_processing_config(self, team_name: str, players: List[Player]) -> None:
        """
        渲染球员处理配置
        
        Args:
            team_name: 球队名称
            players: 球员列表
        """
        st.markdown("### 个性化处理配置")
        st.markdown("*为每个球员选择合适的处理方案，默认为全套处理*")
        
        # 初始化球员处理选项（默认为全套处理）
        for player in players:
            if f"process_option_{player.id}" not in st.session_state:
                st.session_state[f"process_option_{player.id}"] = "全套处理"
        
        # 使用网格布局显示球员
        cols_per_row = 2  # 每行2个球员，给更多空间显示选项
        for i in range(0, len(players), cols_per_row):
            cols = st.columns(cols_per_row)
            for j, player in enumerate(players[i:i+cols_per_row]):
                if j < len(cols):
                    with cols[j]:
                        self._render_player_config_card(team_name, player)
    
    def _render_player_config_card(self, team_name: str, player: Player) -> None:
        """
        渲染球员配置卡片
        
        Args:
            team_name: 球队名称
            player: 球员对象
        """
        # 球员信息容器
        with st.container():
            st.markdown(f"#### {player.name} (#{player.jersey_number})")
            
            # 显示球员照片
            if player.photo:
                photo_path = self.photo_service.get_photo_path(team_name, player.photo)
                if photo_path:
                    st.image(photo_path, width=200)
                else:
                    st.error("照片不存在")
            else:
                st.info("无照片")
            
            # 处理选项选择
            current_option = st.session_state.get(f"process_option_{player.id}", "全套处理")
            
            selected_option = st.selectbox(
                "处理方案",
                options=list(ProcessOptions.OPTIONS.keys()),
                index=list(ProcessOptions.OPTIONS.keys()).index(current_option),
                key=f"process_option_{player.id}",
                format_func=lambda x: ProcessOptions.OPTIONS[x]["label"]
            )
            
            # 显示选项说明
            option_info = ProcessOptions.OPTIONS[selected_option]
            st.markdown(f"**说明：** {option_info['description']}")
            
            st.markdown("---")
    
    def render_processing_execution(self, team_name: str, players: List[Player], 
                                  clothes_image) -> None:
        """
        渲染处理执行区域
        
        Args:
            team_name: 球队名称
            players: 球员列表
            clothes_image: 模板图片
        """
        # 处理统计
        st.markdown("### 处理统计")
        
        # 收集处理配置
        processing_configs = {}
        for player in players:
            option_key = st.session_state.get(f"process_option_{player.id}", "全套处理")
            processing_configs[player.id] = option_key
        
        # 统计各种处理类型的球员数量
        process_stats = self.photo_service.get_processing_stats(processing_configs)
        needs_template = any(
            ProcessOptions.needs_template(option) 
            for option in processing_configs.values()
        )
        
        # 显示统计信息
        st.markdown("**处理方案统计：**")
        for option, count in process_stats.items():
            if count > 0:
                option_info = ProcessOptions.OPTIONS[option]
                st.markdown(f"- {option_info['label']}: {count}人")
        
        # 处理按钮和验证
        st.markdown("### 开始处理")
        
        # 验证条件
        processing_players = [
            p for p in players 
            if processing_configs.get(p.id, "不处理") != "不处理"
        ]
        
        # 验证处理配置
        validation_errors = self.photo_service.validate_batch_processing_config(
            processing_players, processing_configs, clothes_image
        )
        
        if validation_errors:
            for error in validation_errors:
                st.error(f"❌ {error}")
        else:
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("🚀 开始AI处理", type="primary"):
                    self._start_processing(team_name, players, processing_configs, clothes_image)
            
            with col2:
                if st.button("❌ 取消处理"):
                    st.session_state.batch_mode = 'normal'
                    st.rerun()
        
        # 处理历史记录
        st.markdown("### 处理历史")
        st.info("📝 处理历史记录功能开发中...")
    
    def _start_processing(self, team_name: str, players: List[Player],
                         processing_configs: Dict[str, str], clothes_image) -> None:
        """
        开始处理
        
        Args:
            team_name: 球队名称
            players: 球员列表
            processing_configs: 处理配置
            clothes_image: 模板图片
        """
        # 创建处理配置
        config = self.photo_service.create_processing_config(
            team_name, players, processing_configs, clothes_image
        )
        
        if not config:
            st.error("创建处理配置失败")
            return
        
        # 显示处理信息
        processing_players = [
            p for p in players 
            if processing_configs.get(p.id, "不处理") != "不处理"
        ]
        
        st.info(f"将对 {len(processing_players)} 名球员进行个性化处理")
        
        # 显示处理进度
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # 开始处理
        try:
            status_text.text("🚀 正在启动AI照片处理...")
            
            # 模拟处理过程
            result = self.photo_service.simulate_processing(config)
            
            # 显示处理结果
            st.warning("⚠️ AI照片处理功能需要异步环境支持")
            st.info("💡 建议：")
            st.markdown("""
            1. **本地处理**：可以将配置导出，使用独立的处理脚本
            2. **后台服务**：部署独立的照片处理服务
            3. **队列系统**：使用任务队列进行异步处理
            """)
            
            # 显示详细的处理配置
            with st.expander("查看详细处理配置", expanded=False):
                st.json(config.to_dict())
            
            progress_bar.progress(100)
            status_text.text("✅ 个性化处理配置已生成")
            
        except Exception as e:
            st.error(f"处理失败: {e}")
            status_text.text("❌ 处理失败")
