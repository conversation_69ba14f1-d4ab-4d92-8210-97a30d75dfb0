#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入测试脚本
Import Test Script

测试所有模块的导入是否正常
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_imports():
    """测试所有模块导入"""
    print("🧪 开始测试模块导入...")
    
    try:
        # 测试配置模块
        print("📁 测试配置模块...")
        from config.settings import app_settings
        from config.constants import ProcessOptions
        print("✅ 配置模块导入成功")
        
        # 测试数据模型
        print("📁 测试数据模型...")
        from models.team import Team
        from models.player import Player
        from models.processing_config import ProcessingConfig
        print("✅ 数据模型导入成功")
        
        # 测试工具模块
        print("📁 测试工具模块...")
        from utils.image_utils import ImageProcessor
        from utils.validation import DataValidator
        from utils.helpers import get_safe_team_name
        print("✅ 工具模块导入成功")
        
        # 测试数据访问层
        print("📁 测试数据访问层...")
        from data.team_repository import TeamRepository
        from data.player_repository import PlayerRepository
        from data.file_manager import FileManager
        print("✅ 数据访问层导入成功")
        
        # 测试业务逻辑层
        print("📁 测试业务逻辑层...")
        from services.team_service import TeamService
        from services.player_service import PlayerService
        from services.photo_service import PhotoService
        from services.ai_service import AIService
        from services.export_service import ExportService
        print("✅ 业务逻辑层导入成功")
        
        # 测试UI组件层
        print("📁 测试UI组件层...")
        from components.sidebar import SidebarComponent
        from components.player_form import PlayerFormComponent
        from components.batch_upload import BatchUploadComponent
        from components.photo_processing import PhotoProcessingComponent
        from components.ai_chat import AIChatComponent
        from components.player_list import PlayerListComponent
        print("✅ UI组件层导入成功")
        
        print("\n🎉 所有模块导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 开始测试基本功能...")
    
    try:
        # 测试配置
        from config.settings import app_settings
        print(f"✅ 应用标题: {app_settings.PAGE_TITLE}")
        
        # 测试球队服务
        from services.team_service import TeamService
        team_service = TeamService()
        teams = team_service.get_teams_list()
        print(f"✅ 球队列表: {teams}")
        
        # 测试球员模型
        from models.player import Player
        player = Player.create_new("测试球员", "99")
        print(f"✅ 创建球员: {player.display_name}")
        
        print("✅ 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🚀 球队管理系统模块化测试")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试基本功能
        func_success = test_basic_functionality()
        
        if func_success:
            print("\n🎉 所有测试通过！系统可以正常运行。")
            print("💡 现在可以运行: streamlit run app.py")
        else:
            print("\n❌ 功能测试失败")
    else:
        print("\n❌ 导入测试失败")
    
    print("=" * 50)
