#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试团队创建功能
Debug Team Creation Function
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'streamlit_team_management_modular'))

def test_team_creation():
    """测试团队创建功能"""
    print("🔍 调试团队创建功能...")
    
    try:
        # 模拟 streamlit session state
        class MockSessionState:
            def __init__(self):
                self.data = {}
                self.user_id = 'default_user'
            
            def get(self, key, default=None):
                return self.data.get(key, default)
            
            def __setitem__(self, key, value):
                self.data[key] = value
            
            def __getitem__(self, key):
                return self.data[key]
            
            def __contains__(self, key):
                return key in self.data
        
        # 创建模拟的 streamlit 模块
        import streamlit as st
        if not hasattr(st, 'session_state'):
            st.session_state = MockSessionState()
        
        # 确保设置用户ID
        st.session_state.user_id = 'default_user'
        
        print(f"✅ 用户ID设置为: {st.session_state.user_id}")
        
        # 导入服务
        from services.team_service import TeamService
        
        team_service = TeamService()
        print("✅ TeamService 创建成功")
        
        # 测试获取用户ID
        user_id = team_service._get_current_user_id()
        print(f"✅ 获取到用户ID: {user_id}")
        
        # 测试创建团队
        test_team_name = "测试球队"
        print(f"🏗️ 尝试创建团队: {test_team_name}")
        
        success, message = team_service.create_team(test_team_name)
        
        if success:
            print(f"✅ 团队创建成功: {message}")
        else:
            print(f"❌ 团队创建失败: {message}")
        
        # 测试获取团队列表
        teams = team_service.get_teams_list()
        print(f"📋 当前团队列表: {teams}")
        
        return success
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_folder():
    """测试数据文件夹"""
    print("\n📁 检查数据文件夹...")
    
    try:
        from config.settings import app_settings
        
        data_folder = app_settings.paths.DATA_FOLDER
        print(f"📂 数据文件夹路径: {data_folder}")
        
        if not os.path.exists(data_folder):
            print("📁 创建数据文件夹...")
            os.makedirs(data_folder, exist_ok=True)
        
        # 检查用户文件夹
        user_folder = os.path.join(data_folder, 'default_user')
        if not os.path.exists(user_folder):
            print("📁 创建用户文件夹...")
            os.makedirs(user_folder, exist_ok=True)
        
        # 检查团队文件夹
        teams_folder = os.path.join(user_folder, 'teams')
        if not os.path.exists(teams_folder):
            print("📁 创建团队文件夹...")
            os.makedirs(teams_folder, exist_ok=True)
        
        print("✅ 数据文件夹结构正常")
        return True
        
    except Exception as e:
        print(f"❌ 数据文件夹检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始调试团队创建功能\n")
    
    # 测试数据文件夹
    folder_ok = test_data_folder()
    
    # 测试团队创建
    creation_ok = test_team_creation()
    
    print(f"\n{'='*50}")
    print(f"📊 调试结果:")
    print(f"📁 数据文件夹: {'✅ 正常' if folder_ok else '❌ 异常'}")
    print(f"🏗️ 团队创建: {'✅ 正常' if creation_ok else '❌ 异常'}")
    print('='*50)
    
    if folder_ok and creation_ok:
        print("🎉 团队创建功能正常！")
        print("\n💡 建议:")
        print("1. 刷新浏览器页面")
        print("2. 清除浏览器缓存")
        print("3. 重新尝试创建团队")
    else:
        print("⚠️ 发现问题，请检查上述错误信息")
    
    return folder_ok and creation_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
