#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球员列表组件
Player List Component

提供球员列表展示和管理的UI组件
"""

import streamlit as st
import os
from typing import List

from models.player import Player
from services.player_service import PlayerService
from services.photo_service import PhotoService
from services.ai_service import AIService
from config.settings import app_settings


class PlayerListComponent:
    """球员列表组件"""

    def __init__(self):
        self.player_service = PlayerService()
        # 获取当前用户ID用于PhotoService
        user_id = self._get_current_user_id()
        self.photo_service = PhotoService(user_id)
        self.ai_service = AIService(user_id)

    def _get_current_user_id(self) -> str:
        """获取当前用户ID"""
        return st.session_state.get('user_id', '')
    
    def render_empty_team_guide(self, team_name: str) -> None:
        """
        渲染空球队引导界面
        
        Args:
            team_name: 球队名称
        """
        st.markdown("---")
        st.markdown(f"### ⚽ 为球队「{team_name}」添加球员")
        st.info("👋 球队已创建成功！现在开始为您的球队添加球员吧：")
        
        # 步骤指引
        col1, col2 = st.columns([1, 2])
        
        with col1:
            st.markdown("#### 📋 添加球员步骤")
            st.markdown("""
            **第1步：选择添加方式**
            - 推荐使用"批量添加"上传所有球员照片
            - 或使用"单个添加"逐个添加球员

            **第2步：填写球员信息**
            - 为每张照片标注姓名和号码
            - 设置AI处理方案

            **第3步：完成建档**
            - 系统自动生成球员档案
            - 准备AI处理和报名表生成
            """)
        
        with col2:
            st.markdown("#### 🎯 开始添加球员")
            
            # 大按钮引导
            if st.button("🚀 开始批量添加球员", type="primary", use_container_width=True):
                st.session_state.batch_mode = 'upload'
                st.session_state.batch_photos = []
                st.session_state.show_add_form = False
                st.rerun()
            
            st.markdown("*推荐：一次性上传所有球员照片，快速建立完整档案*")
            
            st.markdown("---")
            
            # 单个添加选项
            if st.button("➕ 单个添加球员", use_container_width=True):
                st.session_state.show_add_form = True
                st.session_state.batch_mode = 'normal'
                st.rerun()
            
            st.markdown("*适合：只添加少数球员或补充球员信息*")
            
            st.markdown("---")
            
            # 提示信息
            st.markdown("💡 **提示**：如需创建其他球队，请使用左侧边栏的创建球队功能")
    
    def render_team_stats(self, players: List[Player]) -> None:
        """
        渲染球队统计信息
        
        Args:
            players: 球员列表
        """
        if not players:
            return
        
        col1, col2, col3 = st.columns([2, 2, 2])
        
        with col1:
            st.metric("👥 球员总数", len(players))
        
        with col2:
            # 计算有照片的球员数量
            players_with_photos = len([p for p in players if p.has_photo])
            st.metric("📸 已上传照片", f"{players_with_photos}/{len(players)}")
        
        with col3:
            # 显示球队状态
            if players_with_photos == len(players):
                st.success("✅ 球队档案完整")
            else:
                st.warning(f"⚠️ 还有 {len(players) - players_with_photos} 人未上传照片")
    
    def render_management_buttons(self, team_name: str) -> None:
        """
        渲染管理按钮
        
        Args:
            team_name: 球队名称
        """
        # 主要操作按钮
        col_create, col_title = st.columns([1, 2])
        
        with col_create:
            st.markdown("### 📝 创建新球队")
        
        with col_title:
            st.markdown("### 🎯 球员管理操作")
        
        col1, col2, col3, col4 = st.columns([1.5, 0.5, 1, 1])
        
        with col1:
            new_team_name_main = st.text_input(
                "球队名称", 
                placeholder="请输入球队名称", 
                key="main_team_name", 
                label_visibility="collapsed"
            )
        
        with col2:
            if st.button("创建", use_container_width=True, key="main_create_team"):
                if new_team_name_main.strip():
                    from services.team_service import TeamService
                    team_service = TeamService()
                    success, message = team_service.create_team(new_team_name_main.strip())
                    
                    if success:
                        st.success(message)
                        st.session_state.current_team = new_team_name_main.strip()
                        st.rerun()
                    else:
                        st.error(message)
                else:
                    st.error("请输入球队名称")
        
        with col3:
            if st.button("➕ 添加球员", use_container_width=True):
                st.session_state.show_add_form = True
                st.session_state.batch_mode = 'normal'
                st.rerun()
        
        with col4:
            if st.button("📤 批量添加", use_container_width=True):
                st.session_state.batch_mode = 'upload'
                st.session_state.batch_photos = []
                st.session_state.show_add_form = False
                st.rerun()
    
    def render_player_cards(self, team_name: str, players: List[Player]) -> None:
        """
        渲染球员卡片网格
        
        Args:
            team_name: 球队名称
            players: 球员列表
        """
        if not players:
            return
        
        st.subheader("球员名单")
        
        # 创建球员卡片网格
        cols_per_row = app_settings.PLAYERS_PER_ROW
        for i in range(0, len(players), cols_per_row):
            cols = st.columns(cols_per_row)
            
            for j, col in enumerate(cols):
                if i + j < len(players):
                    player = players[i + j]
                    
                    with col:
                        self._render_player_card(team_name, player)
    
    def _render_player_card(self, team_name: str, player: Player) -> None:
        """
        渲染单个球员卡片
        
        Args:
            team_name: 球队名称
            player: 球员对象
        """
        # 创建球员卡片
        with st.container():
            st.markdown(f"### {player.name}")
            
            # 显示球员照片
            if player.photo:
                photo_path = self.photo_service.get_photo_path(team_name, player.photo)
                if photo_path:
                    st.image(photo_path, width=200)
                else:
                    st.info("照片文件不存在")
            else:
                st.info("暂无照片")
            
            # 球员信息
            st.markdown(f"**球衣号码**: {player.jersey_number}")
            st.markdown(f"**创建时间**: {player.created_at[:10]}")
            
            # 操作按钮
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button("✏️ 编辑", key=f"edit_{player.id}", use_container_width=True):
                    st.session_state.edit_player_id = player.id
                    st.session_state.show_edit_form = True
                    st.rerun()

            with col2:
                # 换装按钮
                if st.button("🎨 换装", key=f"tryon_{player.id}", use_container_width=True):
                    if player.photo:
                        st.session_state[f"show_tryon_{player.id}"] = True
                        st.rerun()
                    else:
                        st.warning("该球员暂无照片，无法进行换装")

            with col3:
                if st.button("🗑️ 删除", key=f"delete_{player.id}", use_container_width=True):
                    # 确认删除
                    if st.session_state.get(f"confirm_delete_{player.id}", False):
                        success, message = self.player_service.delete_player(team_name, player.id)
                        if success:
                            st.success(message)
                            # 清除确认状态
                            if f"confirm_delete_{player.id}" in st.session_state:
                                del st.session_state[f"confirm_delete_{player.id}"]
                            st.rerun()
                        else:
                            st.error(message)
                    else:
                        # 设置确认状态
                        st.session_state[f"confirm_delete_{player.id}"] = True
                        st.warning(f"再次点击确认删除 {player.name}")
                        st.rerun()

            # 换装界面
            if st.session_state.get(f"show_tryon_{player.id}", False):
                self._render_fashion_tryon_interface(team_name, player)
    
    def render_player_list(self, team_name: str) -> None:
        """
        渲染完整的球员列表界面
        
        Args:
            team_name: 球队名称
        """
        players = self.player_service.get_players(team_name)
        
        if not players:
            # 空球队引导界面
            self.render_empty_team_guide(team_name)
        else:
            # 已有球员的管理界面
            st.markdown("---")
            
            # 球员统计
            self.render_team_stats(players)
            
            # 管理按钮
            self.render_management_buttons(team_name)
            
            # 球员卡片
            self.render_player_cards(team_name, players)

    def _render_fashion_tryon_interface(self, team_name: str, player: Player) -> None:
        """
        渲染换装界面

        Args:
            team_name: 球队名称
            player: 球员对象
        """
        st.markdown("---")
        st.markdown(f"### 🎨 为 {player.name} 进行AI换装")

        # 检查换装功能是否可用
        if not self.ai_service.is_fashion_tryon_available():
            st.error("❌ 换装功能不可用，请检查 fashion_tryon_toolkit 是否正确安装")
            if st.button("关闭", key=f"close_tryon_{player.id}"):
                st.session_state[f"show_tryon_{player.id}"] = False
                st.rerun()
            return

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 📸 当前球员照片")
            if player.photo:
                photo_path = self.photo_service.get_photo_path(team_name, player.photo)
                if photo_path:
                    st.image(photo_path, width=250)
                else:
                    st.error("照片文件不存在")

        with col2:
            st.markdown("#### 👕 选择球衣模板")

            # 球衣模板选择
            clothes_options = {
                "红色球衣": "fashion_tryon_toolkit/test_photos/red_jersey.jpg",
                "蓝色球衣": "fashion_tryon_toolkit/test_photos/blue_jersey.jpg",
                "白色球衣": "fashion_tryon_toolkit/test_photos/white_jersey.jpg",
                "自定义上传": "upload"
            }

            selected_clothes = st.selectbox(
                "选择球衣模板",
                options=list(clothes_options.keys()),
                key=f"clothes_select_{player.id}"
            )

            clothes_path = None
            if selected_clothes == "自定义上传":
                uploaded_clothes = st.file_uploader(
                    "上传球衣图片",
                    type=['jpg', 'jpeg', 'png'],
                    key=f"clothes_upload_{player.id}"
                )
                if uploaded_clothes:
                    # 保存上传的球衣图片
                    import os
                    clothes_dir = os.path.join("fashion_tryon_toolkit", "temp_clothes")
                    os.makedirs(clothes_dir, exist_ok=True)
                    clothes_path = os.path.join(clothes_dir, f"clothes_{player.id}.jpg")
                    with open(clothes_path, "wb") as f:
                        f.write(uploaded_clothes.getbuffer())
                    st.success("球衣图片上传成功！")
            else:
                clothes_path = clothes_options[selected_clothes]
                # 显示选中的球衣
                if os.path.exists(clothes_path):
                    st.image(clothes_path, width=200, caption=selected_clothes)
                else:
                    st.warning(f"球衣模板 {selected_clothes} 不存在，请选择其他模板或上传自定义球衣")

        # 操作按钮
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🎨 开始换装", key=f"start_tryon_{player.id}", use_container_width=True):
                if clothes_path and os.path.exists(clothes_path):
                    player_photo_path = self.photo_service.get_photo_path(team_name, player.photo)
                    if player_photo_path and os.path.exists(player_photo_path):
                        # 执行换装
                        with st.spinner("正在进行AI换装，请稍候..."):
                            result = self.ai_service.fashion_tryon_single(player_photo_path, clothes_path)

                        if result["success"]:
                            st.success(result["message"])
                            st.markdown(f"**成本**: {result['cost_ptc']:.2f} PTC (约 {result['cost_usd']:.2f} 元)")

                            # 显示处理结果
                            st.markdown("#### 🎉 换装结果")
                            col_result1, col_result2 = st.columns(2)

                            with col_result1:
                                st.markdown("**原始照片**")
                                st.image(player_photo_path, width=200)

                            with col_result2:
                                st.markdown("**换装结果**")
                                st.image(result["result_path"], width=200)

                            # 保存结果选项
                            if st.button("💾 保存为新照片", key=f"save_result_{player.id}"):
                                # 这里可以添加保存逻辑
                                st.info("保存功能待实现")
                        else:
                            st.error(f"换装失败: {result['error']}")
                    else:
                        st.error("球员照片文件不存在")
                else:
                    st.error("请选择有效的球衣模板")

        with col2:
            if st.button("📊 成本预估", key=f"cost_estimate_{player.id}", use_container_width=True):
                st.info("💰 单次换装成本预估：\n- 换装处理: 0.1 PTC\n- 背景移除: 0.5 PTC\n- 总计: 0.6 PTC (约 4.2 元)")

        with col3:
            if st.button("❌ 关闭", key=f"close_tryon_{player.id}", use_container_width=True):
                st.session_state[f"show_tryon_{player.id}"] = False
                st.rerun()
