#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI改进功能
Test AI Improvements

验证AI状态感知功能是否正常工作
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from services.ai_service import AIService
from services.team_service import TeamService

def test_ai_improvements():
    """测试AI改进功能"""
    print("🧪 开始测试AI状态感知功能...")
    
    # 初始化服务
    ai_service = AIService()
    team_service = TeamService()
    
    # 测试不同的球队状态
    test_cases = [
        {
            "name": "空球队",
            "stats": {
                'total_players': 0,
                'players_with_photos': 0,
                'completion_rate': 0.0,
                'is_complete': False
            }
        },
        {
            "name": "部分完成的球队",
            "stats": {
                'total_players': 5,
                'players_with_photos': 3,
                'completion_rate': 60.0,
                'is_complete': False
            }
        },
        {
            "name": "完整的球队",
            "stats": {
                'total_players': 8,
                'players_with_photos': 8,
                'completion_rate': 100.0,
                'is_complete': True
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📊 测试场景：{test_case['name']}")
        print("-" * 50)
        
        # 测试系统提示词生成
        system_prompt = ai_service.get_system_prompt("测试球队", test_case['stats'])
        print(f"✅ 系统提示词长度：{len(system_prompt)} 字符")
        
        # 测试初始消息生成
        initial_messages = ai_service.initialize_chat_messages("测试球队", test_case['stats'])
        print(f"✅ 初始消息数量：{len(initial_messages)}")
        
        # 显示初始助手消息
        assistant_message = initial_messages[1]['content']
        print(f"🤖 AI初始消息预览：")
        print(f"   {assistant_message[:100]}...")
        
        # 测试智能建议生成
        suggestions = ai_service._generate_smart_suggestions(test_case['stats'])
        print(f"💡 智能建议：{suggestions}")
        
        # 测试状态描述
        status = ai_service._get_team_status_description(test_case['stats'])
        print(f"📈 状态描述：{status}")
    
    print("\n🎉 AI状态感知功能测试完成！")
    print("\n📝 测试总结：")
    print("- ✅ 系统提示词可以根据球队状态动态生成")
    print("- ✅ 初始消息会根据球队完成度个性化")
    print("- ✅ 智能建议会根据球队状态提供相应指导")
    print("- ✅ 状态描述准确反映球队当前情况")

if __name__ == "__main__":
    test_ai_improvements()
