#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的换装逻辑演示
Simple Fashion Try-On Logic Demo
"""

import os
from PIL import Image, ImageDraw

def create_simple_demo():
    """创建简单的换装逻辑演示"""
    print("🎨 创建简单的换装逻辑演示...")
    
    os.makedirs("demo", exist_ok=True)
    
    # 1. 创建模特照片示例
    create_model_demo()
    
    # 2. 创建服装图片示例
    create_clothing_demo()
    
    # 3. 创建换装结果示例
    create_result_demo()
    
    print("✅ 演示图片创建完成！")
    print("📁 查看 demo/ 文件夹中的图片")

def create_model_demo():
    """创建模特照片演示"""
    width, height = 400, 500
    
    # 创建模特照片
    img = Image.new('RGB', (width, height), 'lightblue')
    draw = ImageDraw.Draw(img)
    
    # 绘制人物
    # 头部
    draw.ellipse([150, 80, 250, 180], fill='peachpuff', outline='black', width=3)
    
    # 身体 - 白色T恤
    draw.rectangle([120, 180, 280, 350], fill='white', outline='black', width=3)
    
    # 手臂
    draw.rectangle([80, 200, 120, 320], fill='peachpuff', outline='black', width=2)
    draw.rectangle([280, 200, 320, 320], fill='peachpuff', outline='black', width=2)
    
    # 腿部 - 蓝色裤子
    draw.rectangle([140, 350, 190, 450], fill='blue', outline='black', width=2)
    draw.rectangle([210, 350, 260, 450], fill='blue', outline='black', width=2)
    
    # 添加标题
    draw.rectangle([10, 10, 390, 60], fill='white', outline='black', width=2)
    draw.text((20, 25), "1. Model Photo (Person wearing white T-shirt)", fill='black')
    
    img.save("demo/1_model_photo.png")
    print("📸 创建模特照片: demo/1_model_photo.png")

def create_clothing_demo():
    """创建服装图片演示"""
    width, height = 400, 500
    
    # 创建服装图片
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 绘制红色T恤
    # T恤主体
    shirt_points = [
        (100, 150), (300, 150), (320, 170), (320, 400),
        (80, 400), (80, 170)
    ]
    draw.polygon(shirt_points, fill='red', outline='darkred', width=4)
    
    # 袖子
    draw.polygon([(80, 170), (50, 180), (50, 250), (80, 240)], fill='red', outline='darkred', width=3)
    draw.polygon([(320, 170), (350, 180), (350, 250), (320, 240)], fill='red', outline='darkred', width=3)
    
    # 领口
    draw.polygon([(170, 150), (230, 150), (220, 190), (180, 190)], fill='white', outline='darkred', width=3)
    
    # 添加标题
    draw.rectangle([10, 10, 390, 60], fill='white', outline='black', width=2)
    draw.text((20, 25), "2. Clothing Photo (Red T-shirt)", fill='black')
    
    img.save("demo/2_clothing_photo.png")
    print("👕 创建服装图片: demo/2_clothing_photo.png")

def create_result_demo():
    """创建换装结果演示"""
    width, height = 400, 500
    
    # 步骤3: 换装结果
    img3 = Image.new('RGB', (width, height), 'lightblue')
    draw3 = ImageDraw.Draw(img3)
    
    # 人物 - 现在穿红色T恤
    draw3.ellipse([150, 80, 250, 180], fill='peachpuff', outline='black', width=3)
    draw3.rectangle([120, 180, 280, 350], fill='red', outline='black', width=3)  # 红色T恤
    draw3.rectangle([80, 200, 120, 320], fill='peachpuff', outline='black', width=2)
    draw3.rectangle([280, 200, 320, 320], fill='peachpuff', outline='black', width=2)
    draw3.rectangle([140, 350, 190, 450], fill='blue', outline='black', width=2)
    draw3.rectangle([210, 350, 260, 450], fill='blue', outline='black', width=2)
    
    draw3.rectangle([10, 10, 390, 60], fill='white', outline='black', width=2)
    draw3.text((20, 25), "3. After AI Fashion Try-On (Red T-shirt)", fill='black')
    
    img3.save("demo/3_fashion_result.png")
    print("🎯 创建换装结果: demo/3_fashion_result.png")
    
    # 步骤4: 背景移除
    img4 = Image.new('RGBA', (width, height), (255, 255, 255, 0))  # 透明背景
    draw4 = ImageDraw.Draw(img4)
    
    # 只绘制人物，无背景
    draw4.ellipse([150, 80, 250, 180], fill='peachpuff', outline='black', width=3)
    draw4.rectangle([120, 180, 280, 350], fill='red', outline='black', width=3)
    draw4.rectangle([80, 200, 120, 320], fill='peachpuff', outline='black', width=2)
    draw4.rectangle([280, 200, 320, 320], fill='peachpuff', outline='black', width=2)
    draw4.rectangle([140, 350, 190, 450], fill='blue', outline='black', width=2)
    draw4.rectangle([210, 350, 260, 450], fill='blue', outline='black', width=2)
    
    # 添加标题背景
    draw4.rectangle([10, 10, 390, 60], fill='white', outline='black', width=2)
    draw4.text((20, 25), "4. Background Removed (Transparent)", fill='black')
    
    img4.save("demo/4_background_removed.png")
    print("🖼️ 创建去背景结果: demo/4_background_removed.png")
    
    # 步骤5: 白底合成
    img5 = Image.new('RGB', (width, height), 'white')  # 白色背景
    draw5 = ImageDraw.Draw(img5)
    
    # 绘制人物
    draw5.ellipse([150, 80, 250, 180], fill='peachpuff', outline='black', width=3)
    draw5.rectangle([120, 180, 280, 350], fill='red', outline='black', width=3)
    draw5.rectangle([80, 200, 120, 320], fill='peachpuff', outline='black', width=2)
    draw5.rectangle([280, 200, 320, 320], fill='peachpuff', outline='black', width=2)
    draw5.rectangle([140, 350, 190, 450], fill='blue', outline='black', width=2)
    draw5.rectangle([210, 350, 260, 450], fill='blue', outline='black', width=2)
    
    draw5.rectangle([10, 10, 390, 60], fill='lightgray', outline='black', width=2)
    draw5.text((20, 25), "5. Final Result (White Background)", fill='black')
    
    img5.save("demo/5_final_result.png")
    print("⚪ 创建最终结果: demo/5_final_result.png")

def create_comparison_image():
    """创建对比图"""
    print("📊 创建对比图...")
    
    # 加载所有步骤的图片
    images = []
    titles = [
        "Original Model",
        "Clothing Template", 
        "After Try-On",
        "Background Removed",
        "Final Result"
    ]
    
    for i in range(1, 6):
        try:
            if i == 1:
                img = Image.open("demo/1_model_photo.png")
            elif i == 2:
                img = Image.open("demo/2_clothing_photo.png")
            elif i == 3:
                img = Image.open("demo/3_fashion_result.png")
            elif i == 4:
                img = Image.open("demo/4_background_removed.png")
            else:
                img = Image.open("demo/5_final_result.png")
            
            # 调整大小
            img = img.resize((200, 250))
            images.append(img)
        except:
            print(f"无法加载图片 {i}")
    
    if len(images) == 5:
        # 创建拼接图
        total_width = 200 * 5 + 40  # 5张图片 + 间距
        total_height = 300
        
        comparison = Image.new('RGB', (total_width, total_height), 'white')
        
        for i, img in enumerate(images):
            x = i * 210 + 10
            y = 30
            comparison.paste(img, (x, y))
            
            # 添加标题
            draw = ImageDraw.Draw(comparison)
            draw.text((x + 10, 10), titles[i], fill='black')
        
        comparison.save("demo/comparison.png")
        print("📊 创建对比图: demo/comparison.png")

def main():
    """主函数"""
    print("🎯 换装逻辑简单演示")
    print("=" * 50)
    
    create_simple_demo()
    create_comparison_image()
    
    print("\n📋 生成的演示文件：")
    print("  1️⃣ 1_model_photo.png - 原始模特照片（穿白色T恤）")
    print("  2️⃣ 2_clothing_photo.png - 服装模板（红色T恤）")
    print("  3️⃣ 3_fashion_result.png - AI换装结果（穿红色T恤）")
    print("  4️⃣ 4_background_removed.png - 背景移除结果")
    print("  5️⃣ 5_final_result.png - 最终白底结果")
    print("  📊 comparison.png - 完整对比图")
    
    print("\n🎯 换装逻辑总结：")
    print("  📸 准备：模特照片 + 服装图片")
    print("  🤖 处理：AI换装 → 背景移除 → 白底合成")
    print("  ✅ 结果：模特穿上新服装的白底照片")
    
    print("\n💰 成本：0.6 PTC/张（约4.2元）")
    print("⏱️ 时间：2-5分钟/张")

if __name__ == "__main__":
    main()
