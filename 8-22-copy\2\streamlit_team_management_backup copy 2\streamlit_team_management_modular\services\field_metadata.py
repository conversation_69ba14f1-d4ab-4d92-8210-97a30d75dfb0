#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段元数据管理系统
基于原始ai_data_collector_release的设计
"""

import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum


class FieldSource(Enum):
    """字段来源枚举"""
    USER_DIRECT = "user_direct"      # 用户直接提供
    AI_SMART_FILL = "ai_smart_fill"  # AI智能填充
    AI_AUTO_COLOR = "ai_auto_color"  # AI自动配色
    SYSTEM_DEFAULT = "system_default" # 系统默认值


class FieldStatus(Enum):
    """字段状态枚举"""
    EMPTY = "empty"           # 空值
    FILLED = "filled"         # 已填充
    CONFIRMED = "confirmed"   # 用户已确认
    MODIFIED = "modified"     # 已修改
    CONFLICTED = "conflicted" # 存在冲突


@dataclass
class FieldMetadata:
    """字段元数据"""
    value: Any = None
    source: FieldSource = FieldSource.SYSTEM_DEFAULT
    status: FieldStatus = FieldStatus.EMPTY
    last_updated: str = ""
    update_count: int = 0
    previous_values: List[Any] = None
    confidence: float = 0.0
    
    def __post_init__(self):
        if self.previous_values is None:
            self.previous_values = []
        if not self.last_updated:
            self.last_updated = datetime.now().isoformat()


class FieldMetadataManager:
    """字段元数据管理器"""
    
    def __init__(self):
        self.metadata_store: Dict[str, Dict[str, FieldMetadata]] = {}
    
    def update_field_with_metadata(
        self, 
        team_id: str, 
        field_path: str, 
        value: Any, 
        source: FieldSource,
        confidence: float = 1.0
    ) -> Dict[str, Any]:
        """
        更新字段并记录元数据
        
        Args:
            team_id: 球队ID
            field_path: 字段路径，如 "basic_info.team_name"
            value: 字段值
            source: 字段来源
            confidence: 置信度
            
        Returns:
            Dict: 更新结果
        """
        if team_id not in self.metadata_store:
            self.metadata_store[team_id] = {}
        
        # 获取现有元数据
        existing_metadata = self.metadata_store[team_id].get(field_path)
        
        if existing_metadata:
            # 保存历史值
            existing_metadata.previous_values.append(existing_metadata.value)
            existing_metadata.value = value
            existing_metadata.source = source
            existing_metadata.status = FieldStatus.MODIFIED
            existing_metadata.last_updated = datetime.now().isoformat()
            existing_metadata.update_count += 1
            existing_metadata.confidence = confidence
        else:
            # 创建新元数据
            self.metadata_store[team_id][field_path] = FieldMetadata(
                value=value,
                source=source,
                status=FieldStatus.FILLED,
                confidence=confidence
            )
        
        return {
            "updated": True,
            "field_path": field_path,
            "value": value,
            "source": source.value,
            "confidence": confidence
        }
    
    def get_field_metadata(self, team_id: str, field_path: str) -> Optional[FieldMetadata]:
        """获取字段元数据"""
        if team_id not in self.metadata_store:
            return None
        return self.metadata_store[team_id].get(field_path)
    
    def get_all_metadata(self, team_id: str) -> Dict[str, FieldMetadata]:
        """获取球队所有字段元数据"""
        return self.metadata_store.get(team_id, {})
    
    def get_smart_fill_summary(self, team_id: str) -> Dict[str, Any]:
        """获取智能填充摘要"""
        metadata = self.get_all_metadata(team_id)
        
        smart_fills = []
        auto_colors = []
        user_inputs = []
        
        for field_path, meta in metadata.items():
            if meta.source == FieldSource.AI_SMART_FILL:
                smart_fills.append({
                    "field": field_path,
                    "value": meta.value,
                    "confidence": meta.confidence
                })
            elif meta.source == FieldSource.AI_AUTO_COLOR:
                auto_colors.append({
                    "field": field_path,
                    "value": meta.value,
                    "confidence": meta.confidence
                })
            elif meta.source == FieldSource.USER_DIRECT:
                user_inputs.append({
                    "field": field_path,
                    "value": meta.value
                })
        
        return {
            "smart_fills": smart_fills,
            "auto_colors": auto_colors,
            "user_inputs": user_inputs,
            "total_fields": len(metadata)
        }
    
    def export_metadata(self, team_id: str) -> Dict[str, Any]:
        """导出元数据"""
        metadata = self.get_all_metadata(team_id)
        
        return {
            "team_id": team_id,
            "export_time": datetime.now().isoformat(),
            "metadata": {
                field_path: {
                    "value": meta.value,
                    "source": meta.source.value,
                    "status": meta.status.value,
                    "last_updated": meta.last_updated,
                    "update_count": meta.update_count,
                    "confidence": meta.confidence,
                    "previous_values": meta.previous_values
                }
                for field_path, meta in metadata.items()
            }
        }


# 创建全局实例
field_metadata_manager = FieldMetadataManager()
