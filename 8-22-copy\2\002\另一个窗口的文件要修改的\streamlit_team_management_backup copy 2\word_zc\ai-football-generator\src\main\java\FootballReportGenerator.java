import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.Pictures;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 足球报名表生成器
 * 集成原有的poi-tl模板功能
 */
public class FootballReportGenerator {
    private String templatePath;
    private String outputDirectory;
    private String photosDirectory;
    
    public FootballReportGenerator(String templatePath, String outputDirectory, String photosDirectory) {
        this.templatePath = templatePath;
        this.outputDirectory = outputDirectory;
        this.photosDirectory = photosDirectory;
        
        // 确保输出目录存在
        createDirectoryIfNotExists(outputDirectory);
    }
    
    /**
     * 生成足球报名表 - 使用原有项目的成熟方法
     */
    public String generateReport(FootballTeamData teamData) {
        try {
            System.out.println("🚀 开始生成足球报名表...");

            // 1. 准备模板数据 - 使用原有项目的方法
            Map<String, Object> data = prepareTemplateDataFromTeamData(teamData);

            // 2. 生成输出文件名
            String outputPath = outputDirectory + "ai_football_registration_" + System.currentTimeMillis() + ".docx";

            // 3. 使用模板生成文档
            try (FileInputStream templateStream = new FileInputStream(templatePath)) {
                XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);

                try (FileOutputStream outputStream = new FileOutputStream(outputPath)) {
                    template.write(outputStream);
                }

                template.close();

                System.out.println("✅ 报名表生成成功：" + outputPath);
                return outputPath;

            }

        } catch (Exception e) {
            System.err.println("❌ 生成报名表失败：" + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 从TeamData准备模板数据 - 使用原有项目的成熟方法
     */
    private Map<String, Object> prepareTemplateDataFromTeamData(FootballTeamData teamData) {
        Map<String, Object> data = new HashMap<>();

        // 添加队伍基本信息
        TeamInfo teamInfo = teamData.getTeamInfo();
        if (teamInfo != null) {
            data.put("title", teamInfo.getTitle() != null ? teamInfo.getTitle() : "足球比赛报名表");
            data.put("organizationName", teamInfo.getOrganizationName() != null ? teamInfo.getOrganizationName() : "");
            data.put("teamLeader", teamInfo.getTeamLeader() != null ? teamInfo.getTeamLeader() : "");
            data.put("coach", teamInfo.getCoach() != null ? teamInfo.getCoach() : "");
            data.put("teamDoctor", teamInfo.getTeamDoctor() != null ? teamInfo.getTeamDoctor() : "");
        }

        // 添加球员信息 - 使用原有项目的addPlayerData方法
        PlayerData[] players = teamData.getPlayers();
        for (int i = 0; i < 10; i++) {
            int playerIndex = i + 1;

            if (i < players.length && players[i] != null) {
                PlayerData player = players[i];

                // 使用原有项目的方法添加球员数据
                addPlayerData(data, playerIndex,
                            player.getNumber() != null ? player.getNumber() : "",
                            player.getName() != null ? player.getName() : "",
                            player.getPhotoPath() != null ? player.getPhotoPath() : "");
            } else {
                // 空球员位置
                data.put("player" + playerIndex + "Number", "");
                data.put("player" + playerIndex + "Name", "");
                // 不添加照片数据，让模板保持原样
            }
        }

        return data;
    }

    /**
     * 添加单个球员数据 - 复制自原有项目的成熟方法
     */
    private void addPlayerData(Map<String, Object> data, int playerIndex,
                              String number, String name, String photoPath) {
        try {
            // 添加球员号码
            data.put("player" + playerIndex + "Number", number);

            // 添加球员姓名
            data.put("player" + playerIndex + "Name", name);

            // 处理球员照片 - 先裁剪再添加
            if (photoPath != null && !photoPath.trim().isEmpty()) {
                String processedPhotoPath = cropImageToSquare(photoPath, playerIndex);
                data.put("player" + playerIndex + "Photo",
                    Pictures.ofLocal(processedPhotoPath)
                        .size(100, 120)  // 设置图片大小：宽100px，高120px
                        .create());

                System.out.println("✅ 添加球员数据：" + number + "号 " + name + " (照片: " + processedPhotoPath + ")");
            } else {
                System.out.println("✅ 添加球员数据：" + number + "号 " + name + " (无照片)");
            }

        } catch (Exception e) {
            System.err.println("⚠️ 无法加载照片 " + photoPath + "，将跳过该球员的照片");
            // 如果照片加载失败，只添加文本信息
            data.put("player" + playerIndex + "Number", number);
            data.put("player" + playerIndex + "Name", name);
            // 不添加照片数据，模板中会显示占位符
        }
    }
    
    /**
     * 将图片裁剪为正方形（居中裁剪）- 复制自原有项目
     * 支持PNG和JPG格式，这样可以避免图片拉伸变形
     */
    private String cropImageToSquare(String imagePath, int playerIndex) throws Exception {
        File imageFile = new File(imagePath);
        if (!imageFile.exists()) {
            throw new IOException("图片文件不存在: " + imagePath);
        }

        BufferedImage originalImage = ImageIO.read(imageFile);
        if (originalImage == null) {
            throw new IOException("无法读取图片: " + imagePath);
        }

        int width = originalImage.getWidth();
        int height = originalImage.getHeight();

        // 计算正方形的边长（取较小的尺寸）
        int size = Math.min(width, height);

        // 计算裁剪的起始位置（居中）
        int x = (width - size) / 2;
        int y = (height - size) / 2;

        // 裁剪图片为正方形
        BufferedImage croppedImage = originalImage.getSubimage(x, y, size, size);

        // 根据原图格式确定输出格式和扩展名
        String originalExtension = getFileExtension(imagePath);
        String outputFormat = originalExtension.equalsIgnoreCase("jpg") || originalExtension.equalsIgnoreCase("jpeg") ? "JPG" : "PNG";
        String outputExtension = outputFormat.toLowerCase();

        // 保存裁剪后的图片，保持原格式
        String croppedPath = photosDirectory + "player" + playerIndex + "_cropped." + outputExtension;
        ImageIO.write(croppedImage, outputFormat, new File(croppedPath));

        System.out.println("✂️ 图片已裁剪：" + imagePath + " -> " + croppedPath +
                          " (原尺寸: " + width + "x" + height + " -> 裁剪尺寸: " + size + "x" + size +
                          ", 格式: " + outputFormat + ")");
        return croppedPath;
    }
    
    /**
     * 获取文件扩展名 - 复制自原有项目
     */
    private String getFileExtension(String filePath) {
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex + 1);
        }
        return "";
    }

    /**
     * 创建目录（如果不存在）
     */
    private void createDirectoryIfNotExists(String directoryPath) {
        File directory = new File(directoryPath);
        if (!directory.exists()) {
            directory.mkdirs();
        }
    }
}
