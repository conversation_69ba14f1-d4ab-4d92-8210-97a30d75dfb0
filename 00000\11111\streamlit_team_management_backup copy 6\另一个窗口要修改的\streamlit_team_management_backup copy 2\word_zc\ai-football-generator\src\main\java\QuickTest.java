import java.util.*;

/**
 * 模块化重构后的快速测试程序
 * 测试Word生成核心模块和Python集成适配器
 */
public class QuickTest {
    public static void main(String[] args) {
        System.out.println("🧪 开始模块化重构测试...");
        System.out.println("==================================================");

        // 测试1：Word生成核心模块
        testWordGeneratorCore();

        System.out.println("\n==================================================");

        // 测试2：Python集成适配器
        testPythonIntegrationAdapter();

        System.out.println("\n🎉 所有测试完成！");
    }

    /**
     * 测试Word生成核心模块
     */
    private static void testWordGeneratorCore() {
        try {
            System.out.println("📝 测试1：Word生成核心模块");

            // 创建Word生成器
            WordGeneratorCore generator = new WordGeneratorCore(
                "template.docx",
                "output",
                "photos"
            );

            // 准备测试数据
            FootballTeamData teamData = createTestTeamData();

            // 生成Word文档
            String result = generator.generateReport(teamData);

            if (result != null) {
                System.out.println("✅ Word生成核心模块测试成功！");
                System.out.println("   生成文件：" + result);
            } else {
                System.err.println("❌ Word生成核心模块测试失败！");
            }

        } catch (Exception e) {
            System.err.println("❌ Word生成核心模块测试出错：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试Python集成适配器
     */
    private static void testPythonIntegrationAdapter() {
        try {
            System.out.println("🐍 测试2：Python集成适配器");

            // 创建Python集成适配器
            PythonIntegrationAdapter adapter = new PythonIntegrationAdapter(
                "template.docx",
                "output",
                "photos"
            );

            // 测试连接
            System.out.println(adapter.testConnection());

            // 准备Python格式的测试数据
            Map<String, String> teamInfo = createPythonTeamInfo();
            List<Map<String, String>> players = createPythonPlayersData();

            // 验证数据
            boolean isValid = adapter.validatePythonData(teamInfo, players);
            System.out.println("数据验证结果：" + (isValid ? "✅ 通过" : "❌ 失败"));

            if (isValid) {
                // 生成Word文档
                String result = adapter.generateReportFromPython(teamInfo, players);

                if (result != null) {
                    System.out.println("✅ Python集成适配器测试成功！");
                    System.out.println("   生成文件：" + result);
                } else {
                    System.err.println("❌ Python集成适配器测试失败！");
                }
            }

        } catch (Exception e) {
            System.err.println("❌ Python集成适配器测试出错：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建测试用的球队数据（Java格式）
     */
    private static FootballTeamData createTestTeamData() {
        // 创建队伍信息
        TeamInfo teamInfo = TeamInfo.fromPythonData(
            "2025年五人制足球比赛报名表",
            "太河镇人民政府",
            "李四",
            "张三",
            "王五"
        );

        // 创建球员数据
        PlayerData[] players = new PlayerData[10];
        players[0] = PlayerData.fromPythonData("10", "张雷", "photos/player1.png");
        players[1] = PlayerData.fromPythonData("0", "白浩", "photos/player2.jpg");
        players[2] = PlayerData.fromPythonData("3", "翟召昌", "photos/player3.jpg");
        players[3] = PlayerData.fromPythonData("11", "赵飞", "photos/player4.jpg");
        players[4] = PlayerData.fromPythonData("5", "王洪艺", "photos/player5.jpg");

        // 创建完整的球队数据
        return FootballTeamData.fromPythonData(teamInfo, players);
    }

    /**
     * 创建Python格式的队伍信息
     */
    private static Map<String, String> createPythonTeamInfo() {
        Map<String, String> teamInfo = new HashMap<>();
        teamInfo.put("title", "2025年五人制足球比赛报名表");
        teamInfo.put("organizationName", "太河镇人民政府");
        teamInfo.put("teamLeader", "李四");
        teamInfo.put("coach", "张三");
        teamInfo.put("teamDoctor", "王五");
        return teamInfo;
    }

    /**
     * 创建Python格式的球员数据
     */
    private static List<Map<String, String>> createPythonPlayersData() {
        List<Map<String, String>> players = new ArrayList<>();

        // 球员1
        Map<String, String> player1 = new HashMap<>();
        player1.put("number", "10");
        player1.put("name", "张雷");
        player1.put("photoPath", "photos/player1.png");
        players.add(player1);

        // 球员2
        Map<String, String> player2 = new HashMap<>();
        player2.put("number", "0");
        player2.put("name", "白浩");
        player2.put("photoPath", "photos/player2.jpg");
        players.add(player2);

        // 球员3
        Map<String, String> player3 = new HashMap<>();
        player3.put("number", "3");
        player3.put("name", "翟召昌");
        player3.put("photoPath", "photos/player3.jpg");
        players.add(player3);

        // 球员4
        Map<String, String> player4 = new HashMap<>();
        player4.put("number", "11");
        player4.put("name", "赵飞");
        player4.put("photoPath", "photos/player4.jpg");
        players.add(player4);

        // 球员5
        Map<String, String> player5 = new HashMap<>();
        player5.put("number", "5");
        player5.put("name", "王洪艺");
        player5.put("photoPath", "photos/player5.jpg");
        players.add(player5);

        return players;
    }


}
