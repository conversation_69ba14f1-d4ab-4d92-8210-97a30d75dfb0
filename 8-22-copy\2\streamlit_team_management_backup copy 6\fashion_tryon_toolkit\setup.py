#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时尚换装工具包安装和配置脚本
Fashion Try-On Toolkit Setup Script
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    else:
        print(f"✅ Python版本检查通过: {sys.version}")
        return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    try:
        # 读取requirements.txt
        requirements_file = Path(__file__).parent / "requirements.txt"
        if not requirements_file.exists():
            print("❌ 未找到requirements.txt文件")
            return False
        
        # 安装依赖
        cmd = [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功")
            return True
        else:
            print(f"❌ 依赖包安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建输出目录...")
    
    directories = [
        "temp_files",
        "results", 
        "batch_results",
        "analysis_reports"
    ]
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        dir_path.mkdir(exist_ok=True)
        print(f"✅ 创建目录: {dir_name}")
    
    return True

def check_config():
    """检查配置文件"""
    print("\n⚙️ 检查配置文件...")
    
    config_file = Path("config.py")
    if not config_file.exists():
        print("❌ 未找到config.py文件")
        return False
    
    # 读取配置文件内容
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查API密钥是否已设置
        if 'API_KEY = "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o"' in content:
            print("⚠️  警告: 检测到示例API密钥，请替换为您的实际密钥")
            print("   编辑config.py文件，修改API_KEY变量")
            return False
        elif 'API_KEY = "your-api-key-here"' in content:
            print("⚠️  警告: 请设置您的API密钥")
            print("   编辑config.py文件，修改API_KEY变量")
            return False
        else:
            print("✅ 配置文件检查通过")
            return True
            
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def run_test():
    """运行简单测试"""
    print("\n🧪 运行配置测试...")
    
    try:
        # 导入配置模块
        sys.path.insert(0, str(Path(__file__).parent))
        from config import validate_config, get_total_cost_per_image, get_total_cost_cny_per_image
        
        # 验证配置
        validate_config()
        
        # 显示成本信息
        cost_ptc = get_total_cost_per_image()
        cost_cny = get_total_cost_cny_per_image()
        
        print("✅ 配置验证通过")
        print(f"💰 单张图片成本: {cost_ptc} PTC (约{cost_cny:.1f}元)")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "="*60)
    print("🎉 安装完成！使用示例:")
    print("="*60)
    
    print("\n📸 单张照片处理:")
    print("python single_fashion_tryon.py --model_image model.jpg --clothes_image clothes.png")
    
    print("\n📁 批量照片处理:")
    print("python batch_fashion_tryon.py --input_folder photos/ --clothes_image clothes.png")
    
    print("\n📖 查看详细文档:")
    print("- README.md - 项目概述")
    print("- API_GUIDE.md - API使用指南") 
    print("- EXAMPLES.md - 详细使用示例")
    
    print("\n⚠️  重要提醒:")
    print("1. 确保已设置正确的API密钥 (编辑config.py)")
    print("2. 准备好模特照片和服装图片")
    print("3. 确保网络连接正常")
    
    print("="*60)

def main():
    """主安装流程"""
    print("🎯 时尚换装工具包安装程序")
    print("Fashion Try-On Toolkit Setup")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 创建目录
    if not create_directories():
        return False
    
    # 检查配置
    config_ok = check_config()
    
    # 运行测试 (只有配置正确时才运行)
    if config_ok:
        test_ok = run_test()
    else:
        test_ok = False
    
    # 显示使用示例
    show_usage_examples()
    
    if config_ok and test_ok:
        print("\n🎉 安装和配置完全成功！工具包已准备就绪。")
        return True
    else:
        print("\n⚠️  安装完成，但需要完成配置后才能使用。")
        print("   请按照上述提示设置API密钥。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
