#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球队数据仓库
Team Repository

负责球队数据的持久化和访问
"""

import os
import json
from typing import List, Optional, Dict, Any
import streamlit as st

from models.team import Team, TeamInfo
from config.settings import app_settings
from utils.helpers import get_safe_team_name


class TeamRepository:
    """球队数据仓库"""

    def __init__(self, user_id: str = None):
        if user_id:
            # 用户特定的数据文件夹
            self.data_folder = os.path.join(app_settings.paths.DATA_FOLDER, user_id, 'teams')
        else:
            # 全局数据文件夹（兼容旧版本）
            self.data_folder = app_settings.paths.DATA_FOLDER
        
    def _get_team_file_path(self, team_name: str) -> str:
        """获取球队数据文件路径"""
        safe_name = get_safe_team_name(team_name)
        if 'teams' in self.data_folder:
            # 用户特定路径，直接使用球队名称
            return os.path.join(self.data_folder, f'{safe_name}.json')
        else:
            # 全局路径，使用team_前缀（兼容旧版本）
            return os.path.join(self.data_folder, f'team_{safe_name}.json')
    
    def load_team(self, team_name: str = "default") -> Optional[Team]:
        """
        加载球队数据
        
        Args:
            team_name: 球队名称
            
        Returns:
            Optional[Team]: 球队对象，如果失败返回None
        """
        data_file = self._get_team_file_path(team_name)
        
        try:
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return Team.from_dict(data)
            else:
                # 返回空球队
                return Team.create_empty(team_name)
        except Exception as e:
            st.error(f"加载球队数据失败: {e}")
            return Team.create_empty(team_name)
    
    def save_team(self, team: Team) -> bool:
        """
        保存球队数据
        
        Args:
            team: 球队对象
            
        Returns:
            bool: 是否保存成功
        """
        data_file = self._get_team_file_path(team.name)
        
        try:
            # 确保数据目录存在
            os.makedirs(self.data_folder, exist_ok=True)
            
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(team.to_dict(), f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            st.error(f"保存球队数据失败: {e}")
            return False
    
    def get_teams_list(self) -> List[str]:
        """
        获取所有球队列表
        
        Returns:
            List[str]: 球队名称列表
        """
        teams = []
        
        if os.path.exists(self.data_folder):
            for filename in os.listdir(self.data_folder):
                if filename.startswith('team_') and filename.endswith('.json'):
                    team_name = filename[5:-5]  # 移除 'team_' 前缀和 '.json' 后缀
                    teams.append(team_name)
        
        # 确保默认球队在列表中
        if 'default' not in teams:
            teams.insert(0, 'default')
        
        return teams
    
    def team_exists(self, team_name: str) -> bool:
        """
        检查球队是否存在
        
        Args:
            team_name: 球队名称
            
        Returns:
            bool: 球队是否存在
        """
        data_file = self._get_team_file_path(team_name)
        return os.path.exists(data_file)
    
    def create_team(self, team_name: str) -> bool:
        """
        创建新球队
        
        Args:
            team_name: 球队名称
            
        Returns:
            bool: 是否创建成功
        """
        if self.team_exists(team_name):
            return False
        
        empty_team = Team.create_empty(team_name)
        return self.save_team(empty_team)
    
    def delete_team(self, team_name: str) -> bool:
        """
        删除球队
        
        Args:
            team_name: 球队名称
            
        Returns:
            bool: 是否删除成功
        """
        if team_name == 'default':
            return False  # 不允许删除默认球队
        
        data_file = self._get_team_file_path(team_name)
        
        try:
            if os.path.exists(data_file):
                os.remove(data_file)
            return True
        except Exception as e:
            st.error(f"删除球队失败: {e}")
            return False
    
    def get_team_info(self, team_name: str) -> Optional[TeamInfo]:
        """
        获取球队基本信息
        
        Args:
            team_name: 球队名称
            
        Returns:
            Optional[TeamInfo]: 球队信息，如果失败返回None
        """
        team = self.load_team(team_name)
        return team.team_info if team else None
