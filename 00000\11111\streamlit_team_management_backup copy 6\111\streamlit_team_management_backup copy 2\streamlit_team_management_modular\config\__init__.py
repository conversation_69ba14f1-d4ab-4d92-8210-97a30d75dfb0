"""
配置模块
Configuration Module

提供应用程序的配置管理功能
"""

from config.settings import AppSettings, PathSettings, AISettings, WordGeneratorSettings, app_settings
from config.constants import ProcessOptions, FileTypes, UIConstants

# Word生成器配置
WORD_CONFIG = {
    "jar_path": app_settings.word_generator.JAR_PATH,
    "template_path": app_settings.word_generator.TEMPLATE_PATH,
    "output_dir": app_settings.word_generator.OUTPUT_DIR
}

__all__ = [
    'AppSettings',
    'PathSettings',
    'AISettings',
    'WordGeneratorSettings',
    'ProcessOptions',
    'FileTypes',
    'UIConstants',
    'WORD_CONFIG'
]
