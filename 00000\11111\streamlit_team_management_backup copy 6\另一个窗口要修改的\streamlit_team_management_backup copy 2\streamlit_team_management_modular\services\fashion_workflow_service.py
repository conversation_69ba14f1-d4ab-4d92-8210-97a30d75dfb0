#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
换装工作流服务
Fashion Workflow Service

实现全自动的工作流：用户输入 → AI整理 → 自动触发换装 → 结果展示
"""

import os
import json
import streamlit as st
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path

from services.ai_service import AIService
from services.team_service import TeamService
from services.player_service import PlayerService
from services.auth_service import AuthService


class FashionWorkflowService:
    """换装工作流服务 - 连接AI数据和换装功能"""
    
    def __init__(self, user_id: str = None):
        """初始化工作流服务"""
        self.auth_service = AuthService()
        self.user_id = user_id or self.auth_service.get_current_user_id()
        
        self.ai_service = AIService(self.user_id)
        self.team_service = TeamService()
        self.player_service = PlayerService()
        
        # 工作流状态存储路径
        self.workflow_data_path = os.path.join(
            self.auth_service.get_user_data_path(self.user_id),
            "fashion_workflow"
        )
        os.makedirs(self.workflow_data_path, exist_ok=True)
    
    def check_fashion_readiness(self, team_name: str) -> Dict[str, Any]:
        """
        检查球队是否准备好进行换装
        
        Args:
            team_name: 球队名称
            
        Returns:
            Dict: 准备状态信息
        """
        try:
            # 1. 检查AI导出数据
            ai_export_data = self._load_ai_export_data(team_name)
            if not ai_export_data:
                return {
                    "ready": False,
                    "reason": "no_ai_data",
                    "message": "未找到AI整理的球队数据"
                }
            
            # 2. 检查球员照片
            players_with_photos = []
            players_without_photos = []
            
            for player in ai_export_data.get("players", []):
                photo_info = player.get("photo_info", {})
                if photo_info.get("exists", False) and photo_info.get("absolute_path"):
                    players_with_photos.append(player)
                else:
                    players_without_photos.append(player)
            
            # 3. 评估准备状态
            total_players = len(ai_export_data.get("players", []))
            ready_players = len(players_with_photos)
            
            if ready_players == 0:
                return {
                    "ready": False,
                    "reason": "no_photos",
                    "message": f"球队有{total_players}名球员，但没有球员照片",
                    "total_players": total_players,
                    "ready_players": ready_players
                }
            
            # 4. 检查换装功能可用性
            if not self.ai_service.is_fashion_tryon_available():
                return {
                    "ready": False,
                    "reason": "fashion_unavailable",
                    "message": "换装功能不可用",
                    "total_players": total_players,
                    "ready_players": ready_players
                }
            
            return {
                "ready": True,
                "message": f"准备就绪！{ready_players}/{total_players}名球员有照片",
                "total_players": total_players,
                "ready_players": ready_players,
                "players_with_photos": players_with_photos,
                "players_without_photos": players_without_photos,
                "ai_export_data": ai_export_data
            }
            
        except Exception as e:
            return {
                "ready": False,
                "reason": "error",
                "message": f"检查准备状态时出错: {str(e)}"
            }
    
    def trigger_auto_fashion_workflow(self, team_name: str, clothes_image_path: str = None) -> Dict[str, Any]:
        """
        触发自动换装工作流
        
        Args:
            team_name: 球队名称
            clothes_image_path: 衣服图片路径（可选，如果不提供则使用默认）
            
        Returns:
            Dict: 工作流执行结果
        """
        try:
            # 1. 检查准备状态
            readiness = self.check_fashion_readiness(team_name)
            if not readiness["ready"]:
                return {
                    "success": False,
                    "error": readiness["message"],
                    "readiness": readiness
                }
            
            # 2. 准备换装数据
            players_with_photos = readiness["players_with_photos"]
            player_images = [
                player["photo_info"]["absolute_path"] 
                for player in players_with_photos
            ]
            
            # 3. 使用默认衣服图片（如果没有提供）
            if not clothes_image_path:
                clothes_image_path = self._get_default_clothes_image()
            
            if not clothes_image_path or not os.path.exists(clothes_image_path):
                return {
                    "success": False,
                    "error": "未找到衣服图片，请先上传球队服装图片"
                }
            
            # 4. 执行批量换装
            st.info("🎨 开始自动换装流程...")
            
            fashion_result = self.ai_service.fashion_tryon_batch(
                player_images, clothes_image_path
            )
            
            # 5. 保存工作流结果
            workflow_result = {
                "team_name": team_name,
                "execution_time": datetime.now().isoformat(),
                "readiness_check": readiness,
                "fashion_result": fashion_result,
                "clothes_image_path": clothes_image_path,
                "processed_players": len(player_images)
            }
            
            self._save_workflow_result(team_name, workflow_result)
            
            return {
                "success": True,
                "workflow_result": workflow_result,
                "message": f"自动换装完成！处理了{len(player_images)}名球员"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"自动换装工作流执行失败: {str(e)}"
            }
    
    def _load_ai_export_data(self, team_name: str) -> Optional[Dict]:
        """加载AI导出的数据"""
        try:
            # 方法1：查找AI聊天组件保存的数据（新方式）
            user_id = st.session_state.get('user_id', 'default_user')
            ai_chat_data_path = os.path.join(
                'data', user_id, 'enhanced_ai_data', f'{team_name}_ai_data.json'
            )

            if os.path.exists(ai_chat_data_path):
                with open(ai_chat_data_path, 'r', encoding='utf-8') as f:
                    ai_chat_data = json.load(f)

                # 转换AI聊天数据格式为换装所需格式
                if 'extracted_info' in ai_chat_data:
                    return self._convert_ai_chat_data_to_export_format(ai_chat_data, team_name)

            # 方法2：查找传统AI导出文件（兼容旧方式）
            ai_export_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "ai_export",
                f"team_{team_name}_ai_ready.json"
            )

            if os.path.exists(ai_export_path):
                with open(ai_export_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

            return None

        except Exception as e:
            st.error(f"加载AI导出数据失败: {e}")
            return None

    def _convert_ai_chat_data_to_export_format(self, ai_chat_data: Dict, team_name: str) -> Dict:
        """将AI聊天数据转换为换装所需的导出格式"""
        try:
            # 获取球员数据
            team_data = self.team_service.load_team_data_for_user(
                st.session_state.get('user_id', 'default_user'),
                team_name
            )

            players = []
            if team_data and 'players' in team_data:
                for player in team_data['players']:
                    # 构建球员照片信息
                    photo_info = {
                        "exists": bool(player.get('photo')),
                        "filename": player.get('photo', ''),
                        "absolute_path": ""
                    }

                    # 构建照片绝对路径
                    if player.get('photo'):
                        user_id = st.session_state.get('user_id', 'default_user')
                        photo_path = os.path.join('data', user_id, 'photos', player['photo'])
                        if os.path.exists(photo_path):
                            photo_info["absolute_path"] = os.path.abspath(photo_path)

                    players.append({
                        "id": player.get('id', ''),
                        "name": player.get('name', ''),
                        "jersey_number": player.get('jersey_number', ''),
                        "photo_info": photo_info
                    })

            # 构建导出格式
            export_data = {
                "team_info": {
                    "name": team_name,
                    "display_name": team_name,
                    "ai_extracted_info": ai_chat_data.get('extracted_info', {}),
                    "created_at": ai_chat_data.get('created_at', ''),
                    "updated_at": ai_chat_data.get('updated_at', '')
                },
                "players": players,
                "export_time": ai_chat_data.get('updated_at', ''),
                "data_source": "ai_chat_component"
            }

            return export_data

        except Exception as e:
            st.error(f"转换AI聊天数据格式失败: {e}")
            return None
    
    def _get_default_clothes_image(self) -> Optional[str]:
        """获取默认的衣服图片路径"""
        # 可以在这里设置默认的球队服装图片
        # 或者从配置文件中读取
        default_clothes_dir = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "assets",
            "default_clothes"
        )
        
        if os.path.exists(default_clothes_dir):
            for ext in ['.jpg', '.jpeg', '.png']:
                default_path = os.path.join(default_clothes_dir, f"default_jersey{ext}")
                if os.path.exists(default_path):
                    return default_path
        
        return None
    
    def _save_workflow_result(self, team_name: str, result: Dict):
        """保存工作流结果"""
        try:
            result_file = os.path.join(
                self.workflow_data_path,
                f"workflow_{team_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            st.warning(f"保存工作流结果失败: {e}")
    
    def get_workflow_history(self, team_name: str) -> List[Dict]:
        """获取工作流历史记录"""
        try:
            history = []
            for file in os.listdir(self.workflow_data_path):
                if file.startswith(f"workflow_{team_name}_") and file.endswith('.json'):
                    file_path = os.path.join(self.workflow_data_path, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        history.append(json.load(f))
            
            # 按时间排序
            history.sort(key=lambda x: x.get("execution_time", ""), reverse=True)
            return history
            
        except Exception as e:
            st.error(f"获取工作流历史失败: {e}")
            return []
