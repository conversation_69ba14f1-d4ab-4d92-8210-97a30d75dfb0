#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球员数据仓库
Player Repository

负责球员数据的持久化和访问
"""

from typing import List, Optional
import streamlit as st

from models.player import Player
from models.team import Team
from data.team_repository import TeamRepository


class PlayerRepository:
    """球员数据仓库"""

    def __init__(self, user_id: str = None):
        self.team_repo = TeamRepository(user_id)
    
    def add_player(self, team_name: str, player: Player) -> bool:
        """
        添加球员到球队
        
        Args:
            team_name: 球队名称
            player: 球员对象
            
        Returns:
            bool: 是否添加成功
        """
        team = self.team_repo.load_team(team_name)
        if not team:
            return False
        
        # 检查球衣号码是否已存在
        if team.get_player_by_jersey_number(player.jersey_number):
            st.error(f'球衣号码 {player.jersey_number} 已被使用')
            return False
        
        # 添加球员
        if team.add_player(player):
            return self.team_repo.save_team(team)
        
        return False
    
    def update_player(self, team_name: str, player: Player) -> bool:
        """
        更新球员信息
        
        Args:
            team_name: 球队名称
            player: 球员对象
            
        Returns:
            bool: 是否更新成功
        """
        team = self.team_repo.load_team(team_name)
        if not team:
            return False
        
        # 查找并更新球员
        for i, existing_player in enumerate(team.players):
            if existing_player.id == player.id:
                team.players[i] = player
                return self.team_repo.save_team(team)
        
        return False
    
    def delete_player(self, team_name: str, player_id: str) -> bool:
        """
        删除球员
        
        Args:
            team_name: 球队名称
            player_id: 球员ID
            
        Returns:
            bool: 是否删除成功
        """
        team = self.team_repo.load_team(team_name)
        if not team:
            return False
        
        # 查找球员
        player = team.get_player_by_id(player_id)
        if not player:
            st.error('球员不存在')
            return False
        
        # 删除球员
        if team.remove_player(player_id):
            return self.team_repo.save_team(team)
        
        return False
    
    def get_player(self, team_name: str, player_id: str) -> Optional[Player]:
        """
        获取球员信息
        
        Args:
            team_name: 球队名称
            player_id: 球员ID
            
        Returns:
            Optional[Player]: 球员对象，如果不存在返回None
        """
        team = self.team_repo.load_team(team_name)
        if not team:
            return None
        
        return team.get_player_by_id(player_id)
    
    def get_players(self, team_name: str) -> List[Player]:
        """
        获取球队所有球员
        
        Args:
            team_name: 球队名称
            
        Returns:
            List[Player]: 球员列表
        """
        team = self.team_repo.load_team(team_name)
        return team.players if team else []
    
    def get_player_by_jersey_number(self, team_name: str, jersey_number: str) -> Optional[Player]:
        """
        根据球衣号码获取球员
        
        Args:
            team_name: 球队名称
            jersey_number: 球衣号码
            
        Returns:
            Optional[Player]: 球员对象，如果不存在返回None
        """
        team = self.team_repo.load_team(team_name)
        if not team:
            return None
        
        return team.get_player_by_jersey_number(jersey_number)
    
    def get_used_jersey_numbers(self, team_name: str) -> List[str]:
        """
        获取已使用的球衣号码
        
        Args:
            team_name: 球队名称
            
        Returns:
            List[str]: 已使用的球衣号码列表
        """
        team = self.team_repo.load_team(team_name)
        return team.get_used_jersey_numbers() if team else []
    
    def get_players_with_photos(self, team_name: str) -> List[Player]:
        """
        获取有照片的球员
        
        Args:
            team_name: 球队名称
            
        Returns:
            List[Player]: 有照片的球员列表
        """
        players = self.get_players(team_name)
        return [player for player in players if player.has_photo]
    
    def get_players_without_photos(self, team_name: str) -> List[Player]:
        """
        获取没有照片的球员
        
        Args:
            team_name: 球队名称
            
        Returns:
            List[Player]: 没有照片的球员列表
        """
        players = self.get_players(team_name)
        return [player for player in players if not player.has_photo]
