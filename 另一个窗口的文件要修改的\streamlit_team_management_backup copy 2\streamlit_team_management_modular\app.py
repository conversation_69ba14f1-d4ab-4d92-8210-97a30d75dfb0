#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球队管理系统 - 模块化版本
Team Management System - Modular Version

主应用入口文件
"""

import streamlit as st
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入配置
from config.settings import app_settings

# 导入组件
from components.sidebar import SidebarComponent
from components.player_form import PlayerFormComponent
from components.batch_upload import BatchUploadComponent
from components.photo_processing import PhotoProcessingComponent
from components.ai_chat import AIChatComponent
from components.player_list import PlayerListComponent
from components.auth_component import AuthComponent
from components.loading_manager import loading_manager
from utils.smart_cache_manager import smart_cache

# 导入服务
from services.team_service import TeamService
from services.player_service import PlayerService
from services.auth_service import AuthService

# 导入工具
from utils.safe_file_manager import safe_file_manager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def configure_page():
    """配置页面设置"""
    st.set_page_config(
        page_title=app_settings.PAGE_TITLE,
        page_icon=app_settings.PAGE_ICON,
        layout=app_settings.LAYOUT,
        initial_sidebar_state=app_settings.INITIAL_SIDEBAR_STATE
    )


def render_navigation_header(auth_component: AuthComponent = None):
    """渲染导航头部"""
    col1, col2, col3 = st.columns([1, 2, 1])

    with col1:
        st.markdown("### 🏠 球队管理系统")

    with col2:
        st.markdown(
            "<div style='text-align: center; padding: 10px;'>"
            "<strong>当前页面：球队管理</strong>"
            "</div>",
            unsafe_allow_html=True
        )

    with col3:
        # 显示用户信息和功能按钮
        if auth_component:
            auth_component.render_user_info()

        if st.button("🎨 AI图片编辑器", use_container_width=True):
            st.switch_page("pages/AI_Photo_Editor.py")


def render_new_user_guide():
    """渲染新用户引导界面"""
    st.markdown("---")

    # 欢迎信息
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown(
            """
            <div style='text-align: center; padding: 20px; background-color: #f0f8ff; border-radius: 10px; margin: 20px 0;'>
                <h2>🎉 欢迎使用球队管理系统！</h2>
                <p style='font-size: 18px; color: #666;'>让我们开始创建您的第一个球队吧</p>
            </div>
            """,
            unsafe_allow_html=True
        )

    # 创建球队步骤指导
    st.markdown("### 📋 快速开始指南")

    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown(
            """
            #### 🚀 第一步：创建球队

            1. 在左侧边栏找到"创建新球队"
            2. 输入您的球队名称
            3. 点击"创建球队"按钮

            **💡 球队名称建议：**
            - 淄川FC
            - 青春队
            - 冠军队
            - 或任何您喜欢的名称
            """
        )

    with col2:
        st.markdown(
            """
            #### ⚽ 第二步：添加球员

            1. 球队创建后，开始添加球员
            2. 可以选择批量上传或单个添加
            3. 为每个球员上传照片和信息

            **🎯 功能特色：**
            - AI照片处理
            - 自动生成报名表
            - 球员信息管理
            """
        )

    # 强调创建球队
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; padding: 15px; background-color: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;'>
            <h4>👈 请在左侧边栏创建您的第一个球队</h4>
            <p>创建球队后，您就可以开始添加球员和使用所有功能了！</p>
        </div>
        """,
        unsafe_allow_html=True
    )


def initialize_session_state():
    """初始化session state"""
    if 'current_team' not in st.session_state:
        st.session_state.current_team = 'default'

    if 'show_add_form' not in st.session_state:
        st.session_state.show_add_form = False

    if 'batch_mode' not in st.session_state:
        st.session_state.batch_mode = 'normal'  # normal, upload, edit, photo_process

    if 'batch_photos' not in st.session_state:
        st.session_state.batch_photos = []

    # 确保设置默认用户ID（如果没有登录的话）
    if 'user_id' not in st.session_state:
        st.session_state.user_id = 'default_user'

    # 智能缓存管理器已移除以解决数据一致性问题

    if 'smart_cache_l3' not in st.session_state:
        st.session_state.smart_cache_l3 = {}


def main():
    """主应用函数 - 大公司级别的加载体验和错误处理"""
    try:
        # 配置页面
        configure_page()

        # 显示浏览器标签页加载指示器
        loading_manager.render_browser_loading_indicator()

        # 显示顶部进度条
        loading_manager.render_top_progress_bar()

        # 检查是否是首次加载
        if 'app_initialized' not in st.session_state:
            # 首次加载，使用加载管理器
            loading_manager.clear_tasks()

            # 添加初始化任务
            loading_manager.add_task(
                "config", "初始化应用配置",
                initialize_session_state, weight=1.0
            )

            loading_manager.add_task(
                "auth", "验证用户身份",
                lambda: AuthComponent(), weight=2.0
            )

            # 执行加载任务
            results = loading_manager.execute_tasks_with_progress()

            # 标记应用已初始化
            st.session_state.app_initialized = True
            st.session_state.auth_component = results.get('auth')

            # 加载完成后重新运行以显示主界面
            st.rerun()

        # 应用已初始化，直接显示主界面
        auth_component = st.session_state.get('auth_component') or AuthComponent()

        # 检查是否需要认证
        if auth_component.check_auth_required():
            # 显示登录页面
            auth_component.render_welcome_page()
            return

        # 用户已登录，显示主应用
        render_main_app(auth_component)

    except Exception as e:
        # 全局错误处理
        logger.error(f"应用运行时发生错误: {e}")
        safe_file_manager.handle_media_file_error(e, "主应用")

        # 显示用户友好的错误信息
        st.error("🚨 应用遇到了一些问题")
        st.info("🔄 请尝试刷新页面，或联系技术支持")

        # 提供恢复选项
        if st.button("🧹 清理缓存并重试"):
            st.cache_data.clear()
            st.cache_resource.clear()
            # 清除初始化标记，强制重新加载
            if 'app_initialized' in st.session_state:
                del st.session_state.app_initialized
            st.rerun()


def get_components():
    """获取组件实例（每次创建新实例以确保用户隔离）"""
    return {
        'sidebar': SidebarComponent(),
        'player_form': PlayerFormComponent(),
        'batch_upload': BatchUploadComponent(),
        'photo_processing': PhotoProcessingComponent(),
        'ai_chat': AIChatComponent(),
        'player_list': PlayerListComponent()
    }

def get_services():
    """获取服务实例（每次创建新实例以确保用户隔离）"""
    return {
        'team_service': TeamService(),
        'player_service': PlayerService()
    }

def render_main_app(auth_component: AuthComponent):
    """渲染主应用界面 - 大公司级别的加载体验和错误处理"""
    try:
        # 检查是否需要加载组件
        if 'main_components_loaded' not in st.session_state:
            # 使用加载管理器加载组件
            loading_manager.clear_tasks()

            # 添加组件加载任务
            loading_manager.add_task(
                "components", "初始化界面组件",
                get_components, weight=3.0
            )

            loading_manager.add_task(
                "services", "初始化数据服务",
                get_services, weight=2.0
            )

            # 执行加载任务
            results = loading_manager.execute_tasks_with_progress()

            # 检查加载结果
            if 'components' not in results or 'services' not in results:
                st.error("🚨 组件加载失败，请刷新页面重试")
                return

            # 保存到session state
            st.session_state.main_components = results['components']
            st.session_state.main_services = results['services']
            st.session_state.main_components_loaded = True

            # 重新运行以显示主界面
            st.rerun()

        # 从session state获取组件和服务
        components = st.session_state.main_components
        services = st.session_state.main_services

    except Exception as e:
        logger.error(f"加载组件和服务失败: {e}")
        safe_file_manager.handle_media_file_error(e, "组件初始化")
        st.error("🚨 组件加载失败，请刷新页面重试")
        return

    # 提取组件
    sidebar_component = components['sidebar']
    player_form_component = components['player_form']
    batch_upload_component = components['batch_upload']
    photo_processing_component = components['photo_processing']
    ai_chat_component = components['ai_chat']
    player_list_component = components['player_list']

    # 提取服务
    team_service = services['team_service']
    player_service = services['player_service']

    # 渲染导航头部（包含用户信息）
    render_navigation_header(auth_component)

    # 渲染页面标题
    st.title("⚽ 淄川五人制球队管理系统")
    st.markdown("管理球员信息，上传照片，为AI处理做准备")

    # 渲染侧边栏并获取当前球队（使用骨架屏）
    sidebar_placeholder = st.sidebar.empty()

    # 显示侧边栏骨架屏
    with sidebar_placeholder:
        loading_manager.render_skeleton_screen()

    # 加载球队数据
    try:
        current_team = sidebar_component.render_sidebar()
        # 清除骨架屏
        sidebar_placeholder.empty()
    except Exception as e:
        logger.error(f"加载侧边栏失败: {e}")
        st.error("⚠️ 侧边栏加载失败")
        current_team = None

    # 检查用户是否有球队
    if current_team is None:
        # 新用户没有球队，显示创建球队的引导界面
        render_new_user_guide()
        return

    team_display_name = team_service.get_team_display_name(current_team)

    # 主内容区域
    st.subheader(f"当前球队: {team_display_name}")

    # AI信息收集助手
    st.markdown("#### 🤖 AI信息收集助手")

    # 显示增强功能状态
    ai_chat_component.show_enhanced_features_status()

    # AI聊天界面
    ai_chat_component.render_chat_interface(current_team)

    # AI聊天控制按钮
    st.markdown("#### 🎛️ AI助手控制")
    ai_chat_component.render_chat_controls(current_team)

    # 显示AI提取的数据摘要
    ai_chat_component.show_extracted_data_summary(current_team)

    # 集成换装工作流
    ai_chat_component.render_fashion_workflow_integration(current_team)

    # 缓存管理面板（开发者模式）
    if st.sidebar.checkbox("🔧 显示缓存管理", help="开发者选项：查看和管理缓存"):
        render_cache_management_panel()

    st.markdown("---")

    # 获取当前球队的球员（异步加载）
    players_placeholder = st.empty()

    # 显示球员数据骨架屏
    with players_placeholder:
        st.markdown("### 👥 球员列表")
        loading_manager.render_skeleton_screen()

    try:
        # 异步加载球员数据
        players = player_service.get_players(current_team)
        # 清除骨架屏
        players_placeholder.empty()
    except Exception as e:
        logger.error(f"加载球员数据失败: {current_team}, 错误: {e}")
        safe_file_manager.handle_media_file_error(e, f"加载球员数据: {current_team}")
        players = []
        players_placeholder.warning("⚠️ 球员数据加载失败，显示空列表")
    
    # 根据模式渲染不同的界面
    batch_mode = st.session_state.get('batch_mode', 'normal')
    
    if batch_mode == 'upload':
        # 批量上传界面
        batch_upload_component.render_upload_interface()
    
    elif batch_mode == 'edit':
        # 批量编辑界面
        if st.session_state.batch_photos:
            batch_upload_component.render_batch_edit_form(
                current_team, st.session_state.batch_photos, players
            )
        else:
            st.error("没有照片数据，请重新上传")
            if st.button("返回上传"):
                st.session_state.batch_mode = 'upload'
                st.rerun()
    
    elif batch_mode == 'photo_process':
        # 照片处理界面
        photo_processing_component.render_processing_interface(current_team, players)
    
    else:
        # 正常模式
        # 显示球员添加表单（如果需要）
        if st.session_state.get('show_add_form', False):
            player_form_component.render_add_form(current_team)
        
        # 显示球员编辑表单（如果需要）
        elif st.session_state.get('show_edit_form', False):
            edit_player_id = st.session_state.get('edit_player_id')
            if edit_player_id:
                player_form_component.render_edit_form(current_team, edit_player_id)
        
        # 显示球员列表
        player_list_component.render_player_list(current_team)


def render_cache_management_panel():
    """渲染缓存管理面板"""
    st.markdown("### 🔧 缓存管理面板")

    # 获取缓存统计
    cache_stats = smart_cache.get_cache_stats()

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("缓存命中率", cache_stats["hit_rate"])
        st.metric("缓存条目数", cache_stats["l1_cache_size"])

    with col2:
        st.metric("总命中次数", cache_stats["total_hits"])
        st.metric("总未命中次数", cache_stats["total_misses"])

    with col3:
        st.metric("总淘汰次数", cache_stats["total_evictions"])

        # 缓存操作按钮
        if st.button("🗑️ 清除用户缓存"):
            smart_cache.clear_cache(user_only=True)
            st.success("✅ 用户缓存已清除")
            st.rerun()

    # 显示缓存键列表
    if cache_stats["cache_keys"]:
        with st.expander("📋 缓存键列表", expanded=False):
            for i, key in enumerate(cache_stats["cache_keys"], 1):
                st.write(f"{i}. `{key}`")
    else:
        st.info("📭 当前没有缓存数据")

    # 缓存预热
    st.markdown("#### 🔥 缓存预热")
    if st.button("🚀 预热常用缓存"):
        try:
            # 预热球队列表
            team_service = TeamService()
            team_service.get_teams_list()

            # 预热当前球队数据
            current_team = st.session_state.get('current_team')
            if current_team:
                player_service = PlayerService()
                player_service.get_players(current_team)

            st.success("✅ 缓存预热完成")
        except Exception as e:
            st.error(f"❌ 缓存预热失败: {e}")


if __name__ == "__main__":
    main()
