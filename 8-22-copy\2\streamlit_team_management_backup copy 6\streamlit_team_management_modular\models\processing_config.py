#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理配置数据模型
Processing Configuration Data Model

定义照片处理配置相关的数据结构
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, List, Optional
from models.player import Player


@dataclass
class ProcessingStep:
    """处理步骤配置"""
    fashion_tryon: bool = False
    remove_background: bool = False
    add_white_background: bool = False
    
    def to_dict(self) -> Dict[str, bool]:
        """转换为字典"""
        return {
            'fashion_tryon': self.fashion_tryon,
            'remove_background': self.remove_background,
            'add_white_background': self.add_white_background
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, bool]) -> 'ProcessingStep':
        """从字典创建实例"""
        return cls(
            fashion_tryon=data.get('fashion_tryon', False),
            remove_background=data.get('remove_background', False),
            add_white_background=data.get('add_white_background', False)
        )
    
    @classmethod
    def from_option_key(cls, option_key: str) -> 'ProcessingStep':
        """从处理选项键创建实例"""
        from config.constants import ProcessOptions
        option = ProcessOptions.get_option(option_key)
        
        return cls(
            fashion_tryon=option.get('fashion', False),
            remove_background=option.get('background', False),
            add_white_background=option.get('white', False)
        )


@dataclass
class PlayerProcessingConfig:
    """球员处理配置"""
    player_info: Dict[str, Any]
    process_option: str
    process_steps: ProcessingStep
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'player_info': self.player_info,
            'process_option': self.process_option,
            'process_steps': self.process_steps.to_dict()
        }
    
    @classmethod
    def from_player(cls, player: Player, process_option: str) -> 'PlayerProcessingConfig':
        """从球员对象创建配置"""
        player_info = {
            'name': player.name,
            'id': player.id,
            'jersey_number': player.jersey_number
        }
        
        process_steps = ProcessingStep.from_option_key(process_option)
        
        return cls(
            player_info=player_info,
            process_option=process_option,
            process_steps=process_steps
        )


@dataclass
class ProcessingConfig:
    """完整的处理配置"""
    team: str
    template_image: Optional[str] = None
    processing_players: List[PlayerProcessingConfig] = field(default_factory=list)
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def add_player_config(self, player: Player, process_option: str) -> None:
        """添加球员处理配置"""
        player_config = PlayerProcessingConfig.from_player(player, process_option)
        self.processing_players.append(player_config)
    
    def get_players_needing_template(self) -> List[PlayerProcessingConfig]:
        """获取需要模板图的球员配置"""
        return [
            config for config in self.processing_players
            if config.process_steps.fashion_tryon
        ]
    
    def needs_template(self) -> bool:
        """是否需要模板图"""
        return len(self.get_players_needing_template()) > 0
    
    def get_processing_stats(self) -> Dict[str, int]:
        """获取处理统计"""
        stats = {}
        for config in self.processing_players:
            option = config.process_option
            if option not in stats:
                stats[option] = 0
            stats[option] += 1
        return stats
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'team': self.team,
            'template_image': self.template_image,
            'processing_players': [config.to_dict() for config in self.processing_players],
            'created_at': self.created_at
        }
    
    @classmethod
    def create_for_team(cls, team_name: str, template_image: str = None) -> 'ProcessingConfig':
        """为球队创建处理配置"""
        return cls(
            team=team_name,
            template_image=template_image
        )
