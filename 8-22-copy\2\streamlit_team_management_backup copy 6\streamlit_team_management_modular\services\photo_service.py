#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片服务
Photo Service

提供照片处理相关的业务逻辑
"""

import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import streamlit as st

from models.player import Player
from models.processing_config import ProcessingConfig, PlayerProcessingConfig
from models.image_processing import ProcessingType
from data.file_manager import FileManager
from utils.validation import DataValidator
from utils.smart_cache_manager import cache_critical, cache_important, cache_normal, smart_cache
from config.constants import ProcessOptions


class PhotoService:
    """照片服务"""

    def __init__(self, user_id: str = None):
        self.user_id = user_id
        self.file_manager = FileManager(user_id)
    
    def validate_batch_processing_config(self, players: List[Player], 
                                       processing_configs: Dict[str, str],
                                       template_file=None) -> List[str]:
        """
        验证批量处理配置
        
        Args:
            players: 球员列表
            processing_configs: 处理配置字典 {player_id: option_key}
            template_file: 模板文件
            
        Returns:
            List[str]: 错误消息列表
        """
        errors = []
        
        # 检查是否有球员需要处理
        processing_players = [
            player for player in players 
            if processing_configs.get(player.id, "不处理") != "不处理"
        ]
        
        if not processing_players:
            errors.append("没有球员需要处理，请至少为一名球员选择处理方案")
            return errors
        
        # 检查是否需要模板图
        needs_template = False
        for player in processing_players:
            option_key = processing_configs.get(player.id, "不处理")
            if ProcessOptions.needs_template(option_key):
                needs_template = True
                break
        
        if needs_template and not template_file:
            errors.append("有球员需要换装处理，请上传模板图")
        
        # 检查球员照片是否存在
        for player in processing_players:
            if not player.has_photo:
                errors.append(f"球员 {player.name} 没有照片，无法进行处理")
        
        return errors
    
    def create_processing_config(self, team_name: str, players: List[Player],
                               processing_configs: Dict[str, str],
                               template_file=None) -> Optional[ProcessingConfig]:
        """
        创建处理配置
        
        Args:
            team_name: 球队名称
            players: 球员列表
            processing_configs: 处理配置字典
            template_file: 模板文件
            
        Returns:
            Optional[ProcessingConfig]: 处理配置对象
        """
        # 保存模板图片（如果有）
        template_path = None
        if template_file:
            template_filename = f"template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            template_path = self.file_manager.save_template_image(
                template_file, template_filename
            )
        
        # 创建处理配置
        config = ProcessingConfig.create_for_team(team_name, template_path)
        
        # 添加球员配置
        for player in players:
            option_key = processing_configs.get(player.id, "不处理")
            if option_key != "不处理":
                config.add_player_config(player, option_key)
        
        return config

    def get_processing_config(self, team_name: str) -> ProcessingConfig:
        """
        获取球队的处理配置

        Args:
            team_name: 球队名称

        Returns:
            ProcessingConfig: 处理配置对象
        """
        # 创建默认配置
        config = ProcessingConfig(team=team_name)
        return config

    def get_player_processing_configs(self, team_name: str) -> List[PlayerProcessingConfig]:
        """
        获取球员处理配置列表

        Args:
            team_name: 球队名称

        Returns:
            List[PlayerProcessingConfig]: 球员处理配置列表
        """
        # 返回空列表作为默认值
        return []

    def get_processing_stats(self, processing_configs: Dict[str, str]) -> Dict[str, int]:
        """
        获取处理统计
        
        Args:
            processing_configs: 处理配置字典
            
        Returns:
            Dict[str, int]: 统计信息
        """
        stats = {}
        for option_key in processing_configs.values():
            if option_key not in stats:
                stats[option_key] = 0
            stats[option_key] += 1
        return stats
    
    def batch_set_processing_option(self, players: List[Player], 
                                  option_key: str) -> Dict[str, str]:
        """
        批量设置处理选项
        
        Args:
            players: 球员列表
            option_key: 处理选项键
            
        Returns:
            Dict[str, str]: 更新后的配置字典
        """
        return {player.id: option_key for player in players}
    
    def get_photo_path(self, team_name: str, filename: str) -> Optional[str]:
        """
        获取照片路径
        
        Args:
            team_name: 球队名称
            filename: 文件名
            
        Returns:
            Optional[str]: 照片路径
        """
        return self.file_manager.get_photo_path(team_name, filename)
    
    def photo_exists(self, team_name: str, filename: str) -> bool:
        """
        检查照片是否存在
        
        Args:
            team_name: 球队名称
            filename: 文件名
            
        Returns:
            bool: 照片是否存在
        """
        return self.file_manager.photo_exists(team_name, filename)
    
    def get_processing_option_info(self, option_key: str) -> Dict[str, Any]:
        """
        获取处理选项信息
        
        Args:
            option_key: 选项键
            
        Returns:
            Dict[str, Any]: 选项信息
        """
        return ProcessOptions.get_option(option_key)
    
    def get_all_processing_options(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有处理选项
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有处理选项
        """
        return ProcessOptions.OPTIONS
    
    def process_photos_real(self, config: ProcessingConfig) -> Dict[str, Any]:
        """
        真实的照片处理（替换模拟处理）

        Args:
            config: 处理配置

        Returns:
            Dict[str, Any]: 真实处理结果
        """
        from services.ai_image_engine import AIImageEngine
        from models.image_processing import ImageProcessingRequest, ProcessingType

        start_time = datetime.now()
        engine = AIImageEngine()

        result = {
            'team': config.team,
            'total_players': len(config.processing_players),
            'template_image': config.template_image,
            'processing_summary': {},
            'processed_results': [],
            'successful_count': 0,
            'failed_count': 0,
            'actual_time': 0,
            'actual_cost': 0.0,
            'success': True,
            'error': None
        }

        try:
            st.info(f"🎯 开始处理 {len(config.processing_players)} 张照片...")

            # 创建进度条
            progress_bar = st.progress(0)
            status_text = st.empty()

            for i, player_config in enumerate(config.processing_players):
                # 更新进度
                progress = (i + 1) / len(config.processing_players)
                progress_bar.progress(progress)
                status_text.text(f"正在处理第 {i+1}/{len(config.processing_players)} 张照片...")

                # 获取处理类型
                processing_types = self._get_processing_types_from_option(player_config.process_option)

                if not processing_types:
                    continue

                # 创建处理请求
                request = ImageProcessingRequest(
                    source_image_path=player_config.photo_path,
                    processing_types=processing_types,
                    template_image_path=config.template_image if ProcessingType.FASHION_TRYON in processing_types else None
                )

                # 执行真实处理
                processing_result = engine.process_image(request)

                # 记录结果
                player_result = {
                    'player_id': player_config.player_id,
                    'player_name': getattr(player_config, 'player_name', f'球员{i+1}'),
                    'success': processing_result.success,
                    'result_path': processing_result.processed_image_path,
                    'processing_time': processing_result.processing_time,
                    'error': processing_result.error_message
                }

                result['processed_results'].append(player_result)

                if processing_result.success:
                    result['successful_count'] += 1
                    st.success(f"✅ {player_result['player_name']} 处理成功")
                else:
                    result['failed_count'] += 1
                    st.error(f"❌ {player_result['player_name']} 处理失败: {processing_result.error_message}")

                # 统计处理类型
                option = player_config.process_option
                if option not in result['processing_summary']:
                    result['processing_summary'][option] = 0
                result['processing_summary'][option] += 1

            # 计算总时间和成本
            end_time = datetime.now()
            result['actual_time'] = (end_time - start_time).total_seconds()
            result['actual_cost'] = self._calculate_actual_cost(result['processed_results'])

            # 完成处理
            progress_bar.progress(1.0)
            status_text.text("✅ 处理完成！")

            if result['successful_count'] > 0:
                st.success(f"🎉 成功处理 {result['successful_count']} 张照片！")

            if result['failed_count'] > 0:
                st.warning(f"⚠️ {result['failed_count']} 张照片处理失败")

        except Exception as e:
            result['success'] = False
            result['error'] = str(e)
            st.error(f"❌ 批量处理异常: {e}")

        return result

    def _get_processing_types_from_option(self, option: str) -> List[ProcessingType]:
        """根据处理选项获取处理类型列表"""
        type_mapping = {
            "换装": [ProcessingType.FASHION_TRYON],
            "背景去除": [ProcessingType.REMOVE_BACKGROUND],
            "添加白底": [ProcessingType.ADD_WHITE_BACKGROUND],
            "换装+背景去除": [ProcessingType.FASHION_TRYON, ProcessingType.REMOVE_BACKGROUND],
            "换装+白底": [ProcessingType.FASHION_TRYON, ProcessingType.REMOVE_BACKGROUND, ProcessingType.ADD_WHITE_BACKGROUND],
            "完整流程": [ProcessingType.FASHION_TRYON, ProcessingType.REMOVE_BACKGROUND, ProcessingType.ADD_WHITE_BACKGROUND]
        }
        return type_mapping.get(option, [])

    def _calculate_actual_cost(self, results: List[Dict[str, Any]]) -> float:
        """计算实际成本"""
        # 基于实际API调用计算成本
        # 换装: 0.1 PTC, 背景去除: 0.5 PTC, 白底: 免费
        total_cost = 0.0
        for result in results:
            if result['success']:
                # 这里可以根据实际处理类型计算精确成本
                total_cost += 0.6  # 假设平均每张0.6 PTC
        return total_cost

    def simulate_processing(self, config: ProcessingConfig) -> Dict[str, Any]:
        """
        模拟处理过程（用于演示） - 已弃用，请使用 process_photos_real

        Args:
            config: 处理配置

        Returns:
            Dict[str, Any]: 模拟结果
        """
        st.warning("⚠️ 正在使用模拟处理模式，建议使用 process_photos_real 进行真实处理")

        result = {
            'team': config.team,
            'total_players': len(config.processing_players),
            'template_image': config.template_image,
            'processing_summary': {},
            'estimated_time': 0,
            'estimated_cost': 0.0,
            'is_simulation': True  # 标记这是模拟结果
        }

        # 统计处理类型
        for player_config in config.processing_players:
            option = player_config.process_option
            if option not in result['processing_summary']:
                result['processing_summary'][option] = 0
            result['processing_summary'][option] += 1

        # 估算时间和成本（示例）
        result['estimated_time'] = len(config.processing_players) * 30  # 每人30秒
        result['estimated_cost'] = len(config.processing_players) * 0.5  # 每人0.5元

        return result
