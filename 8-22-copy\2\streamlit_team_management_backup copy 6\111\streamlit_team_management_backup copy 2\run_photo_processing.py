#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立照片处理脚本
用于处理球队管理系统导出的球员照片

使用方法:
python run_photo_processing.py --team_data ai_export/team_xxx_ai_ready.json --clothes_image clothes.png
"""

import asyncio
import argparse
import json
import os
import sys
from pathlib import Path
from datetime import datetime
from photo_processor import process_team_photos

def load_team_data(json_file):
    """加载球队数据"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载球队数据失败: {e}")
        return None

def validate_photos(team_data):
    """验证照片文件是否存在"""
    missing_photos = []
    valid_players = []
    
    for player in team_data.get('players', []):
        photo_info = player.get('photo_info', {})
        photo_path = photo_info.get('absolute_path')
        
        if photo_path and os.path.exists(photo_path):
            valid_players.append(player)
        else:
            missing_photos.append(player['name'])
    
    return valid_players, missing_photos

async def main_async():
    """异步主函数"""
    parser = argparse.ArgumentParser(description='球队照片AI处理工具')
    parser.add_argument('--team_data', type=str, required=True, help='球队数据JSON文件路径')
    parser.add_argument('--clothes_image', type=str, help='服装图片路径（换装时需要）')
    parser.add_argument('--fashion', action='store_true', help='启用换装处理')
    parser.add_argument('--background', action='store_true', help='启用背景去除')
    parser.add_argument('--white', action='store_true', help='启用白底添加')
    parser.add_argument('--all', action='store_true', help='启用所有处理类型')
    
    args = parser.parse_args()
    
    # 加载球队数据
    print("📊 加载球队数据...")
    team_data = load_team_data(args.team_data)
    if not team_data:
        return

    team_name = team_data['team_info']['name']
    print(f"🏀 球队: {team_name}")
    print(f"👥 球员数量: {team_data['team_info']['total_players']}")
    
    # 验证照片
    print("\n📸 验证球员照片...")
    valid_players, missing_photos = validate_photos(team_data)
    
    if missing_photos:
        print(f"⚠️  以下球员的照片不存在: {', '.join(missing_photos)}")
    
    if not valid_players:
        print("❌ 没有有效的球员照片，无法处理")
        return
    
    print(f"✅ 找到 {len(valid_players)} 名球员的有效照片")
    
    # 确定处理类型
    if args.all:
        process_fashion = True
        process_background = True
        process_white = True
    else:
        process_fashion = args.fashion
        process_background = args.background
        process_white = args.white
    
    if not any([process_fashion, process_background, process_white]):
        print("❌ 请至少选择一种处理类型 (--fashion, --background, --white 或 --all)")
        return
    
    # 检查服装图片
    clothes_image_path = None
    if process_fashion:
        if not args.clothes_image:
            print("❌ 换装处理需要提供服装图片 (--clothes_image)")
            return
        
        if not os.path.exists(args.clothes_image):
            print(f"❌ 服装图片不存在: {args.clothes_image}")
            return
        
        clothes_image_path = args.clothes_image
        print(f"👕 服装图片: {clothes_image_path}")
    
    # 显示处理信息
    print("\n🎯 处理配置:")
    print(f"   🔄 换装处理: {'✅' if process_fashion else '❌'}")
    print(f"   🖼️ 背景去除: {'✅' if process_background else '❌'}")
    print(f"   ⚪ 白底添加: {'✅' if process_white else '❌'}")
    
    # 估算成本
    cost_per_player = 0
    if process_fashion:
        cost_per_player += 0.1
    if process_background:
        cost_per_player += 0.5
    
    total_cost = cost_per_player * len(valid_players)
    print(f"   💰 预估成本: {total_cost:.1f} PTC (约{total_cost * 7:.1f}元)")
    
    # 确认处理
    print(f"\n📋 即将处理 {len(valid_players)} 名球员的照片")
    confirm = input("是否继续? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 用户取消处理")
        return
    
    # 开始处理
    print("\n🚀 开始AI照片处理...")
    print("=" * 60)
    
    start_time = datetime.now()
    
    try:
        # 转换球员数据格式
        players_for_processing = []
        for player in valid_players:
            players_for_processing.append({
                'id': player['id'],
                'name': player['name'],
                'jersey_number': player['jersey_number'],
                'photo': player['photo_info']['filename']
            })
        
        # 执行处理
        results = await process_team_photos(
            team_name=team_name,
            players=players_for_processing,
            clothes_image_path=clothes_image_path,
            process_fashion=process_fashion,
            process_background=process_background,
            process_white=process_white
        )
        
        # 处理结果
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        successful_results = [r for r in results if r.get('success', False)]
        total_actual_cost = sum(r.get('total_cost', 0) for r in results)
        
        print("\n" + "=" * 60)
        print("🎉 照片处理完成!")
        print("=" * 60)
        print(f"📊 处理统计:")
        print(f"   📸 总球员数: {len(valid_players)}")
        print(f"   ✅ 成功处理: {len(successful_results)}")
        print(f"   🏆 成功率: {len(successful_results)/len(valid_players)*100:.1f}%")
        print(f"   💰 实际成本: {total_actual_cost:.1f} PTC (约{total_actual_cost * 7:.1f}元)")
        print(f"   ⏱️  处理时间: {processing_time:.1f}秒")
        
        # 保存处理报告
        report = {
            "team_info": team_data['team_info'],
            "processing_config": {
                "fashion_tryon": process_fashion,
                "remove_background": process_background,
                "add_white_background": process_white,
                "clothes_image": clothes_image_path
            },
            "results": results,
            "summary": {
                "total_players": len(valid_players),
                "successful_players": len(successful_results),
                "success_rate": len(successful_results)/len(valid_players)*100,
                "total_cost": total_actual_cost,
                "processing_time": processing_time,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat()
            }
        }
        
        report_path = f"processed_photos/{team_name}/processing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 处理报告: {report_path}")
        print(f"📁 结果文件夹: processed_photos/{team_name}/")
        
        # 显示成功处理的球员
        if successful_results:
            print(f"\n✅ 成功处理的球员:")
            for result in successful_results:
                player_name = result['player_info']['name']
                final_results = result.get('final_results', {})
                result_types = list(final_results.keys())
                print(f"   👤 {player_name}: {', '.join(result_types)}")
        
        # 显示失败的球员
        failed_results = [r for r in results if not r.get('success', False)]
        if failed_results:
            print(f"\n❌ 处理失败的球员:")
            for result in failed_results:
                player_name = result['player_info']['name']
                error = result.get('error', '未知错误')
                print(f"   👤 {player_name}: {error}")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 处理过程发生错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """同步主函数入口"""
    try:
        # 检查依赖
        import aiohttp
        import aiofiles
    except ImportError as e:
        print("❌ 缺少异步依赖，请安装:")
        print("pip install aiohttp aiofiles")
        print(f"错误详情: {e}")
        return
    
    # 运行异步主函数
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        print("\n⚠️  用户中断了处理过程")
    except Exception as e:
        print(f"\n❌ 处理过程发生错误: {e}")

if __name__ == "__main__":
    main()
