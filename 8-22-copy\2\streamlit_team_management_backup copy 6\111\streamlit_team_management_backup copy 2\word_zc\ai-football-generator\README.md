# ⚽ 足球Word生成核心模块 - 模块化重构版

专业的足球报名表Word文档生成核心模块，经过模块化重构，移除了重复功能，专为Python集成优化。

## 🎯 重构目标

- ✂️ **移除重复功能**：去除AI对话、命令行界面等与Python足球系统重复的功能
- 🎯 **专注核心价值**：只保留Word文档生成的核心功能
- 🐍 **Python集成优化**：提供专门的Python集成适配器
- 🧩 **模块化设计**：清晰的职责分离，便于维护和扩展

## ✨ 核心功能

- 📄 **专业Word生成**：基于poi-tl模板引擎生成专业报名表
- 🖼️ **图片处理**：自动裁剪球员照片为正方形，支持多种格式
- 🔗 **Python集成**：提供专门的适配器类，简化JPype调用
- ✅ **数据验证**：完整的数据验证和错误处理机制
- 📊 **灵活配置**：支持自定义模板、输出目录等配置

## 🏗️ 模块架构

### 核心类结构

```
src/main/java/
├── WordGeneratorCore.java          # Word生成核心引擎
├── PythonIntegrationAdapter.java   # Python集成适配器
├── PlayerData.java                 # 数据模型（球员、队伍信息）
└── QuickTest.java                  # 模块测试类
```

### 设计原则

- **单一职责**：每个类只负责一个核心功能
- **Python友好**：专门的适配器类简化JPype调用
- **数据驱动**：清晰的数据模型，支持验证和转换
- **错误处理**：完善的异常处理和日志输出

## 🚀 快速开始

### 环境要求

- Java 8 或更高版本
- Maven 3.6 或更高版本
- Python 3.8+ (用于集成测试)
- JPype1 (用于Python集成)

### 安装步骤

1. **编译项目**
   ```bash
   mvn clean compile
   ```

2. **运行Java测试**
   ```bash
   # Windows
   test-core.bat

   # 或手动运行
   mvn exec:java -Dexec.mainClass="QuickTest"
   ```

3. **运行Python集成测试**
   ```bash
   # 安装JPype1
   pip install JPype1

   # 运行Python测试
   python test_python_integration.py
   ```

## 💻 使用示例

### Java直接调用

```java
// 1. 创建Word生成器
WordGeneratorCore generator = new WordGeneratorCore(
    "template.docx",    // 模板路径
    "output",           // 输出目录
    "photos"            // 照片目录
);

// 2. 准备数据
TeamInfo teamInfo = TeamInfo.fromPythonData(
    "2025年五人制足球比赛报名表",
    "太河镇人民政府",
    "李四", "张三", "王五"
);

PlayerData[] players = new PlayerData[10];
players[0] = PlayerData.fromPythonData("10", "张雷", "photos/player1.png");
players[1] = PlayerData.fromPythonData("0", "白浩", "photos/player2.jpg");

FootballTeamData teamData = FootballTeamData.fromPythonData(teamInfo, players);

// 3. 生成Word文档
String outputPath = generator.generateReport(teamData);
```

### Python集成调用

```python
import jpype

# 1. 启动JVM
jpype.startJVM(classpath=['target/classes', 'lib/*.jar'])

# 2. 导入Java类
PythonAdapter = jpype.JClass('PythonIntegrationAdapter')

# 3. 创建适配器
adapter = PythonAdapter("template.docx", "output", "photos")

# 4. 准备Python数据
team_info = {
    "title": "2025年五人制足球比赛报名表",
    "organizationName": "太河镇人民政府",
    "teamLeader": "李四",
    "coach": "张三",
    "teamDoctor": "王五"
}

players_data = [
    {"number": "10", "name": "张雷", "photoPath": "photos/player1.png"},
    {"number": "0", "name": "白浩", "photoPath": "photos/player2.jpg"},
    {"number": "3", "name": "翟召昌", "photoPath": "photos/player3.jpg"}
]

# 5. 转换为Java格式并生成
java_team_info = jpype.java.util.HashMap()
for k, v in team_info.items():
    java_team_info.put(k, v)

java_players = jpype.java.util.ArrayList()
for player in players_data:
    java_player = jpype.java.util.HashMap()
    for k, v in player.items():
        java_player.put(k, v)
    java_players.add(java_player)

# 6. 生成Word文档
output_path = adapter.generateReportFromPython(java_team_info, java_players)
print(f"生成成功: {output_path}")
```

## 📁 项目结构

```
ai-football-generator/
├── src/main/java/
│   ├── AIFootballGenerator.java    # 主程序
│   ├── OpenAIClient.java          # OpenAI API客户端
│   ├── PlayerDataParser.java      # 数据解析器
│   ├── FootballReportGenerator.java # 报名表生成器
│   └── PlayerData.java            # 数据模型
├── photos/                         # 球员照片目录
├── output/                         # 生成的报名表输出目录
├── template.docx                   # Word模板文件
├── config.properties               # 配置文件
├── pom.xml                        # Maven配置
├── run-ai.bat                     # 启动脚本
└── README.md                      # 说明文档
```

## ⚙️ 配置说明

### config.properties

```properties
# OpenAI API配置
openai.api.key=your-api-key-here
openai.model=gpt-4o-mini
openai.max.tokens=2000
openai.temperature=0.7

# 文件路径配置
template.path=template.docx
output.directory=output/
photos.directory=photos/
```

## 🎯 使用技巧

### 1. 球员信息输入

可以用多种方式输入球员信息：

```
# 方式1：逐个输入
"第一个球员是10号张雷，照片是player1.png"

# 方式2：批量输入
"球员有：10号张雷、9号李四、8号王五，照片分别是player1.png、player2.jpg、player3.jpg"

# 方式3：表格形式
"球员信息如下：
10号 张雷 photos/player1.png
9号 李四 photos/player2.jpg
8号 王五 photos/player3.jpg"
```

### 2. 照片文件

- 支持PNG、JPG、JPEG格式
- 照片会自动裁剪为正方形
- 建议照片尺寸不小于200x200像素

### 3. 数据修改

如果信息有误，可以直接告诉AI：
```
"刚才的教练名字错了，应该是赵六"
"请把10号球员的照片改为player10.jpg"
```

## 🔧 技术架构

- **OpenAI GPT-4o-mini**：自然语言处理和数据提取
- **poi-tl**：Word模板引擎
- **Jackson**：JSON数据处理
- **Apache POI**：Office文档操作
- **Java ImageIO**：图片处理

## ❓ 常见问题

**Q: API调用失败怎么办？**
A: 检查网络连接和API密钥是否正确，确保有足够的API额度。

**Q: 照片无法显示？**
A: 确保照片文件存在且路径正确，支持的格式为PNG、JPG、JPEG。

**Q: 生成的报名表格式不对？**
A: 检查template.docx模板文件是否包含正确的标签格式。

**Q: 如何修改模板样式？**
A: 用Word打开template.docx，修改样式后保存，保持标签格式不变。

## 📄 许可证

本项目基于Apache License 2.0开源协议。

---

**版本**：1.0.0  
**更新时间**：2025年8月
