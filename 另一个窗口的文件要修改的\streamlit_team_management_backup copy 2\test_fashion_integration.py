#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
换装功能集成测试脚本
Fashion Try-on Integration Test Script

测试换装功能是否正确集成到球队管理系统中
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'streamlit_team_management_modular'))

def test_imports():
    """测试导入是否成功"""
    print("🔍 测试导入...")
    
    try:
        from services.ai_service import AIService
        print("✅ AIService 导入成功")
    except ImportError as e:
        print(f"❌ AIService 导入失败: {e}")
        return False
    
    try:
        from components.player_list import PlayerListComponent
        print("✅ PlayerListComponent 导入成功")
    except ImportError as e:
        print(f"❌ PlayerListComponent 导入失败: {e}")
        return False
    
    return True

def test_fashion_tryon_availability():
    """测试换装功能是否可用"""
    print("\n🎨 测试换装功能可用性...")
    
    try:
        from services.ai_service import AIService
        ai_service = AIService()
        
        if ai_service.is_fashion_tryon_available():
            print("✅ 换装功能可用")
            return True
        else:
            print("❌ 换装功能不可用")
            return False
    except Exception as e:
        print(f"❌ 测试换装功能时出错: {e}")
        return False

def test_fashion_tryon_toolkit_files():
    """测试 fashion_tryon_toolkit 文件是否存在"""
    print("\n📁 测试 fashion_tryon_toolkit 文件...")
    
    base_path = os.path.join(os.path.dirname(__file__), 'fashion_tryon_toolkit')
    
    required_files = [
        'single_fashion_tryon.py',
        'config.py',
        'requirements.txt'
    ]
    
    all_exist = True
    for file in required_files:
        file_path = os.path.join(base_path, file)
        if os.path.exists(file_path):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
            all_exist = False
    
    return all_exist

def test_config_access():
    """测试配置访问"""
    print("\n⚙️ 测试配置访问...")

    try:
        # 直接导入 fashion_tryon_toolkit 的配置
        import importlib.util
        fashion_config_path = os.path.join(os.path.dirname(__file__), 'fashion_tryon_toolkit', 'config.py')
        spec = importlib.util.spec_from_file_location("fashion_config", fashion_config_path)
        fashion_config = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(fashion_config)

        print(f"✅ API_KEY: {fashion_config.API_KEY[:10]}...")
        print(f"✅ BASE_URL: {fashion_config.BASE_URL}")
        print(f"✅ API_COSTS: {fashion_config.API_COSTS}")

        return True
    except ImportError as e:
        print(f"❌ 配置导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 配置访问出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始换装功能集成测试\n")
    
    tests = [
        ("导入测试", test_imports),
        ("文件存在测试", test_fashion_tryon_toolkit_files),
        ("配置访问测试", test_config_access),
        ("换装功能可用性测试", test_fashion_tryon_availability),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！换装功能集成成功！")
        print("\n📋 下一步:")
        print("1. 运行 streamlit run streamlit_team_management_modular/app.py")
        print("2. 创建球队并添加球员")
        print("3. 点击球员卡片中的 '🎨 换装' 按钮")
        print("4. 选择球衣模板并开始换装")
    else:
        print("⚠️ 部分测试失败，请检查集成配置")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
