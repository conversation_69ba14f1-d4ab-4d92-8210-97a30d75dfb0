#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档内容验证
验证生成的Word文档是否符合模板要求和包含正确的数据
"""

import os
import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path

def find_latest_generated_file():
    """查找最新生成的Word文件"""
    output_dir = "word_output"
    
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录不存在: {output_dir}")
        return None
    
    # 查找所有.docx文件（排除临时文件）
    docx_files = []
    for file_path in Path(output_dir).glob("*.docx"):
        # 排除Word临时文件（以~$开头）
        if file_path.name.startswith('~$'):
            continue

        stat = file_path.stat()
        docx_files.append({
            'path': str(file_path),
            'name': file_path.name,
            'size': stat.st_size,
            'modified': stat.st_mtime
        })
    
    if not docx_files:
        print("❌ 没有找到任何Word文档")
        return None
    
    # 按修改时间排序，获取最新的
    docx_files.sort(key=lambda x: x['modified'], reverse=True)
    latest_file = docx_files[0]
    
    print(f"📁 找到最新文件: {latest_file['name']}")
    print(f"📏 文件大小: {latest_file['size'] / 1024:.1f} KB")
    
    return latest_file['path']

def extract_word_content(docx_path):
    """提取Word文档的文本内容"""
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            # 读取主文档内容
            document_xml = zip_file.read('word/document.xml')
            
            # 解析XML
            root = ET.fromstring(document_xml)
            
            # 提取所有文本内容
            texts = []
            for elem in root.iter():
                if elem.text:
                    texts.append(elem.text.strip())
            
            # 过滤空文本
            texts = [t for t in texts if t]
            
            return texts
            
    except Exception as e:
        print(f"❌ 提取文档内容失败: {e}")
        return []

def verify_document_structure(docx_path):
    """验证文档结构"""
    print("\n🔍 验证文档结构...")
    print("=" * 50)
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            file_list = zip_file.namelist()
            
            # 检查必要的文件
            required_files = [
                'word/document.xml',
                '[Content_Types].xml',
                'word/_rels/document.xml.rels'
            ]
            
            for req_file in required_files:
                if req_file in file_list:
                    print(f"✅ {req_file}")
                else:
                    print(f"❌ 缺少: {req_file}")
                    return False
            
            # 检查是否有图片
            image_files = [f for f in file_list if f.startswith('word/media/')]
            print(f"📸 找到 {len(image_files)} 个图片文件")
            
            for img_file in image_files:
                img_data = zip_file.read(img_file)
                print(f"   📷 {img_file}: {len(img_data) / 1024:.1f} KB")
            
            return True
            
    except Exception as e:
        print(f"❌ 文档结构验证失败: {e}")
        return False

def verify_content_data(texts):
    """验证文档内容数据"""
    print("\n📝 验证文档内容...")
    print("=" * 50)
    
    # 将所有文本合并为一个字符串进行搜索
    full_text = ' '.join(texts)
    
    # 检查必要的内容
    checks = [
        ("标题", ["2025", "五人制足球", "报名表"]),
        ("球队名称", ["淄川区太河镇足球队"]),
        ("管理人员", ["张建国", "李明华", "王医生"]),
        ("球员姓名", ["张雷", "李明", "王强", "赵飞", "刘洋"]),
        ("球衣号码", ["10", "9", "8", "7", "6"]),
    ]
    
    results = []
    for check_name, keywords in checks:
        found_keywords = []
        for keyword in keywords:
            if keyword in full_text:
                found_keywords.append(keyword)
        
        if found_keywords:
            print(f"✅ {check_name}: 找到 {len(found_keywords)}/{len(keywords)} 项")
            for kw in found_keywords:
                print(f"   - {kw}")
            results.append(True)
        else:
            print(f"❌ {check_name}: 未找到任何项")
            results.append(False)
    
    return all(results)

def verify_template_compliance(texts):
    """验证模板合规性"""
    print("\n📋 验证模板合规性...")
    print("=" * 50)
    
    full_text = ' '.join(texts)
    
    # 检查模板特定的元素
    template_elements = [
        ("表格结构", ["序号", "姓名", "号码"]),
        ("组织信息", ["单位", "领队", "教练", "队医"]),
        ("比赛信息", ["五人制足球", "比赛"]),
        ("年份信息", ["2025"]),
    ]
    
    compliance_score = 0
    total_checks = len(template_elements)
    
    for element_name, keywords in template_elements:
        found = any(keyword in full_text for keyword in keywords)
        if found:
            print(f"✅ {element_name}: 符合")
            compliance_score += 1
        else:
            print(f"❌ {element_name}: 不符合")
    
    compliance_rate = (compliance_score / total_checks) * 100
    print(f"\n📊 模板合规率: {compliance_rate:.1f}% ({compliance_score}/{total_checks})")
    
    return compliance_rate >= 80  # 80%以上认为合规

def analyze_document_quality(docx_path):
    """分析文档质量"""
    print("\n⭐ 分析文档质量...")
    print("=" * 50)
    
    try:
        file_size = os.path.getsize(docx_path)
        
        # 文件大小分析
        if file_size < 100 * 1024:  # 小于100KB
            print("⚠️ 文件较小，可能缺少图片或内容")
            size_score = 60
        elif file_size > 5 * 1024 * 1024:  # 大于5MB
            print("⚠️ 文件较大，可能有冗余内容")
            size_score = 70
        else:
            print("✅ 文件大小合理")
            size_score = 100
        
        # 结构完整性
        structure_ok = verify_document_structure(docx_path)
        structure_score = 100 if structure_ok else 0
        
        # 内容提取
        texts = extract_word_content(docx_path)
        if texts:
            print(f"✅ 成功提取 {len(texts)} 个文本片段")
            content_score = 100
        else:
            print("❌ 无法提取文本内容")
            content_score = 0
        
        # 数据验证
        data_ok = verify_content_data(texts) if texts else False
        data_score = 100 if data_ok else 0
        
        # 模板合规性
        template_ok = verify_template_compliance(texts) if texts else False
        template_score = 100 if template_ok else 0
        
        # 计算总分
        total_score = (size_score + structure_score + content_score + data_score + template_score) / 5
        
        print(f"\n📊 质量评分:")
        print(f"   文件大小: {size_score}/100")
        print(f"   文档结构: {structure_score}/100")
        print(f"   内容提取: {content_score}/100")
        print(f"   数据验证: {data_score}/100")
        print(f"   模板合规: {template_score}/100")
        print(f"   总体评分: {total_score:.1f}/100")
        
        return total_score
        
    except Exception as e:
        print(f"❌ 文档质量分析失败: {e}")
        return 0

def main():
    """主验证函数"""
    print("🔍 Word文档内容验证")
    print("=" * 60)
    print("验证生成的Word文档是否符合模板要求")
    print("=" * 60)
    
    # 查找最新文件
    latest_file = find_latest_generated_file()
    
    if not latest_file:
        print("❌ 没有找到可验证的Word文档")
        return
    
    print(f"\n📄 验证文件: {os.path.basename(latest_file)}")
    
    # 分析文档质量
    quality_score = analyze_document_quality(latest_file)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 验证结果总结")
    print("=" * 60)
    
    if quality_score >= 90:
        print("🎉 优秀！Word文档完全符合要求！")
        grade = "A+"
    elif quality_score >= 80:
        print("✅ 良好！Word文档基本符合要求")
        grade = "A"
    elif quality_score >= 70:
        print("⚠️ 一般，Word文档部分符合要求")
        grade = "B"
    elif quality_score >= 60:
        print("⚠️ 较差，Word文档勉强可用")
        grade = "C"
    else:
        print("❌ 失败，Word文档不符合要求")
        grade = "F"
    
    print(f"\n🏆 文档评级: {grade} ({quality_score:.1f}/100)")
    
    if quality_score >= 80:
        print("\n✅ 验证通过！生成的Word文档符合模板要求！")
        print("💡 文档包含:")
        print("   - 正确的球队信息")
        print("   - 完整的球员数据")
        print("   - 球员照片嵌入")
        print("   - 标准的报名表格式")
        print("   - 符合模板结构")
        print(f"\n📁 文档位置: {latest_file}")
        print("🎯 可以投入实际使用！")
    else:
        print("\n⚠️ 验证未完全通过，建议检查和改进")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
