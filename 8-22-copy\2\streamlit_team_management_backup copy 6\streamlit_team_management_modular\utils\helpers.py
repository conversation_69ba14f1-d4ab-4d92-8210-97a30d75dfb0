#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
辅助函数
Helper Functions

提供通用的辅助函数
"""

import os
from typing import List, Optional


def get_safe_team_name(team_name: str) -> str:
    """
    获取安全的球队名称（用于文件名）
    
    Args:
        team_name: 原始球队名称
        
    Returns:
        str: 安全的球队名称
    """
    if not team_name:
        return "default"
    
    # 只保留字母、数字、空格、连字符和下划线
    safe_name = "".join(c for c in team_name if c.isalnum() or c in (' ', '-', '_')).strip()
    
    # 如果处理后为空，返回默认值
    return safe_name if safe_name else "default"


def format_jersey_numbers(numbers: List[str]) -> str:
    """
    格式化球衣号码列表为显示字符串
    
    Args:
        numbers: 球衣号码列表
        
    Returns:
        str: 格式化的字符串
    """
    if not numbers:
        return "无"
    
    # 转换为整数并排序
    try:
        int_numbers = [int(num) for num in numbers if num.isdigit()]
        int_numbers.sort()
        return ', '.join(map(str, int_numbers))
    except (ValueError, TypeError):
        # 如果转换失败，直接返回原始列表
        return ', '.join(numbers)


def ensure_directory_exists(directory_path: str) -> bool:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory_path: 目录路径
        
    Returns:
        bool: 是否成功
    """
    try:
        if not os.path.exists(directory_path):
            os.makedirs(directory_path)
        return True
    except Exception:
        return False


def get_file_extension(filename: str) -> Optional[str]:
    """
    获取文件扩展名
    
    Args:
        filename: 文件名
        
    Returns:
        Optional[str]: 扩展名（不包含点），如果没有扩展名返回None
    """
    if not filename or '.' not in filename:
        return None
    
    return filename.split('.')[-1].lower()


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小为人类可读的字符串
    
    Args:
        size_bytes: 文件大小（字节）
        
    Returns:
        str: 格式化的文件大小
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"


def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    截断文本到指定长度
    
    Args:
        text: 原始文本
        max_length: 最大长度
        suffix: 截断后缀
        
    Returns:
        str: 截断后的文本
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不安全的字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    if not filename:
        return "unnamed"
    
    # 移除或替换不安全的字符
    unsafe_chars = '<>:"/\\|?*'
    safe_filename = filename
    
    for char in unsafe_chars:
        safe_filename = safe_filename.replace(char, '_')
    
    # 移除多余的空格和点
    safe_filename = safe_filename.strip('. ')
    
    return safe_filename if safe_filename else "unnamed"


def is_valid_image_extension(filename: str) -> bool:
    """
    检查文件是否为有效的图片扩展名
    
    Args:
        filename: 文件名
        
    Returns:
        bool: 是否为有效的图片文件
    """
    if not filename:
        return False
    
    extension = get_file_extension(filename)
    if not extension:
        return False
    
    valid_extensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp']
    return extension in valid_extensions
