# 🤖 AI足球报名表生成器使用指南

## 🎯 功能概述

这是一个基于OpenAI GPT-4o-mini的智能足球报名表生成工具。您只需要用自然语言告诉AI球员信息，AI会自动解析并生成专业的Word报名表。

## 🚀 快速开始

### 1. 环境准备
- 确保已安装Java 8+和Maven 3.6+
- 确保网络连接正常（需要访问OpenAI API）

### 2. 启动程序
```bash
# 方式1：使用启动脚本
run-ai.bat

# 方式2：手动运行
mvn exec:java -Dexec.mainClass="AIFootballGenerator"
```

### 3. 与AI对话
程序启动后，AI会主动询问您的队伍信息。您可以用自然语言回答。

## 💬 对话示例

### 示例1：完整对话流程

```
🤖 AI助手：你好！我是足球报名表生成助手。请告诉我您的队伍信息...

👤 您：我们要参加2025年五人制足球比赛，单位是太河镇人民政府

🤖 AI助手：好的，我记录了比赛信息。请告诉我领队、教练和队医的姓名。

👤 您：领队是李四，教练是张三，队医是王五

🤖 AI助手：谢谢！现在请告诉我球员信息，包括号码、姓名和照片文件名。

👤 您：球员有：10号张雷、0号白浩、3号翟召昌，照片分别是player1.png、player2.jpg、player3.jpg

🤖 AI助手：信息收集完成！以下是整理好的数据：
{
  "teamInfo": {
    "title": "2025年五人制足球比赛报名表",
    "organizationName": "太河镇人民政府",
    "teamLeader": "李四",
    "coach": "张三",
    "teamDoctor": "王五"
  },
  "players": [
    {"number": "10", "name": "张雷", "photoPath": "photos/player1.png"},
    {"number": "0", "name": "白浩", "photoPath": "photos/player2.jpg"},
    {"number": "3", "name": "翟召昌", "photoPath": "photos/player3.jpg"}
  ]
}

📋 解析到的队伍信息：
   🏆 比赛标题：2025年五人制足球比赛报名表
   🏢 单位名称：太河镇人民政府
   👨‍💼 领队：李四
   👨‍🏫 教练：张三
   👨‍⚕️ 队医：王五

👥 球员信息：
   1. 10号 张雷 (照片: photos/player1.png)
   2. 0号 白浩 (照片: photos/player2.jpg)
   3. 3号 翟召昌 (照片: photos/player3.jpg)
   📊 总计：3 名球员

❓ 是否生成报名表？(y/n): y

🚀 正在生成报名表...
🎉 报名表生成成功！
📄 文件位置：output/ai_football_registration_1755411234567.docx
```

### 示例2：批量输入球员信息

```
👤 您：球员信息如下：
10号 张雷 photos/player1.png
9号 李四 photos/player2.jpg
8号 王五 photos/player3.jpg
7号 赵六 photos/player4.jpg
6号 孙七 photos/player5.jpg
```

### 示例3：修正错误信息

```
👤 您：刚才的教练名字错了，应该是赵教练

🤖 AI助手：好的，我已经更新教练信息为赵教练。

👤 您：还有，10号球员的照片应该是player10.jpg

🤖 AI助手：明白了，已将10号球员的照片更新为player10.jpg。
```

## 📁 文件准备

### 照片文件
1. 将球员照片放入 `photos/` 目录
2. 支持格式：PNG、JPG、JPEG
3. 建议尺寸：不小于200x200像素
4. 文件命名：建议使用 `player1.jpg`、`player2.png` 等格式

### 模板文件
- `template.docx` 是Word模板文件
- 包含所有必要的标签，支持10个球员
- 如需修改样式，用Word打开编辑即可

## ⚙️ 配置说明

### config.properties 配置项

```properties
# OpenAI API配置
openai.api.key=your-api-key-here    # 您的OpenAI API密钥
openai.model=gpt-4o-mini            # AI模型
openai.max.tokens=2000              # 最大token数
openai.temperature=0.7              # 创造性参数

# 文件路径配置
template.path=template.docx         # 模板文件路径
output.directory=output/            # 输出目录
photos.directory=photos/            # 照片目录
```

## 🎯 使用技巧

### 1. 信息输入技巧
- **逐步输入**：可以分步骤提供信息，AI会引导您
- **批量输入**：也可以一次性提供所有信息
- **自然语言**：用正常的说话方式，不需要特殊格式
- **随时修正**：发现错误可以随时告诉AI修改

### 2. 照片处理
- 程序会自动裁剪照片为正方形
- 保持原始格式（JPG保存为JPG，PNG保存为PNG）
- 如果照片不存在，会在生成时提示

### 3. 数据验证
- AI会验证信息完整性
- 生成前会显示解析结果供确认
- 可以在确认阶段发现并修正问题

## ❓ 常见问题

### Q: AI无法连接怎么办？
A: 检查以下几点：
- 网络连接是否正常
- OpenAI API密钥是否正确
- 是否有足够的API额度

### Q: 照片无法显示？
A: 确保：
- 照片文件存在于指定路径
- 文件格式为PNG、JPG或JPEG
- 文件名与AI解析的路径一致

### Q: 生成的报名表格式不对？
A: 检查：
- template.docx文件是否完整
- 模板中的标签格式是否正确
- 是否有足够的权限写入输出目录

### Q: 如何修改模板样式？
A: 
1. 用Microsoft Word打开template.docx
2. 修改字体、颜色、布局等样式
3. 保存文件，保持标签格式不变

### Q: 支持多少个球员？
A: 当前支持最多10个球员，这是标准足球队的配置。

## 🔧 高级功能

### 1. 连续生成
- 生成一份报名表后，可以选择继续生成其他队伍的报名表
- AI会重置对话，开始新的信息收集

### 2. 数据格式
- AI会将对话内容转换为JSON格式的结构化数据
- 支持复杂的数据验证和错误处理

### 3. 智能解析
- AI能理解多种表达方式
- 自动推断缺失信息
- 智能处理数据格式转换

## 📞 技术支持

如果遇到问题，请检查：
1. 日志输出中的错误信息
2. 配置文件是否正确
3. 网络连接是否正常
4. 文件权限是否足够

---

**版本**：1.0.0  
**更新时间**：2025年8月
