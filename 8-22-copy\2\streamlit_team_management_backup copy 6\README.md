# 球队管理系统 - Streamlit版本

一个基于Streamlit构建的球队管理Web应用，用于管理球员信息、上传照片，为AI处理做准备。

## 功能特性

### 🏀 核心功能
- **多团队管理**: 支持创建和切换多个团队
- **球员管理**: 添加、删除球员信息
- **照片上传**: 支持多种图片格式，自动压缩优化
- **数据导出**: 导出JSON格式数据，便于AI处理
- **数据隔离**: 每个团队的数据完全独立

### 📱 界面特性
- **现代化UI**: 基于Streamlit的美观界面
- **响应式设计**: 自适应不同屏幕尺寸
- **实时预览**: 照片上传即时预览
- **侧边栏管理**: 便捷的团队切换和创建

### 🔧 技术特性
- **图片处理**: 自动压缩和格式转换
- **数据验证**: 球衣号码唯一性检查
- **文件管理**: 安全的文件上传和存储
- **团队隔离**: 按团队分文件夹存储

## 安装和运行

### 1. 安装依赖
```bash
cd streamlit_team_management
pip install -r requirements.txt
```

### 2. 运行应用
```bash
streamlit run app.py
```

### 3. 访问应用
应用会自动在浏览器中打开，默认地址: http://localhost:8501

## 使用说明

### 团队管理
1. **选择团队**: 在左侧边栏选择现有团队
2. **创建团队**: 在侧边栏输入团队名称并点击"创建团队"
3. **团队切换**: 选择不同团队会自动加载对应的球员数据

### 球员管理
1. **添加球员**: 点击"添加球员"按钮，填写信息并上传照片
2. **查看球员**: 球员以卡片形式展示，包含照片和基本信息
3. **删除球员**: 点击球员卡片下的"删除"按钮

### 数据导出
1. **导出数据**: 点击"导出数据"按钮
2. **下载文件**: 点击"下载JSON文件"获取标准化数据

## 文件结构

```
streamlit_team_management/
├── app.py                 # 主应用文件
├── requirements.txt       # Python依赖
├── README.md             # 说明文档
├── data/                 # 数据存储文件夹
│   ├── team_default.json # 默认团队数据
│   ├── team_红队.json    # 红队数据
│   └── ...               # 其他团队数据
└── uploads/              # 照片存储文件夹
    ├── default/          # 默认团队照片
    ├── 红队/             # 红队照片
    └── ...               # 其他团队照片
```

## 数据格式

### 球员数据结构
```json
{
  "id": "唯一标识符",
  "name": "球员姓名",
  "jersey_number": "球衣号码",
  "photo": "照片文件名",
  "created_at": "创建时间",
  "updated_at": "更新时间"
}
```

### 导出数据格式
```json
{
  "team_info": {
    "name": "团队名称",
    "created_at": "创建时间"
  },
  "players": [
    {
      "id": "球员ID",
      "name": "球员姓名",
      "jersey_number": "球衣号码",
      "photo_path": "uploads/团队名/照片文件名",
      "photo_file": "照片文件名"
    }
  ],
  "export_time": "导出时间",
  "total_players": "球员总数"
}
```

## 特色功能

### 多团队支持
- 每个团队的数据完全隔离
- 球衣号码验证只在同一团队内进行
- 照片按团队分文件夹存储

### 图片处理
- 支持多种图片格式
- 自动压缩到800x800像素以内
- 转换为JPEG格式优化存储

### 数据安全
- 文件名使用UUID避免冲突
- 安全的文件路径处理
- 数据验证和错误处理

## 与AI处理的集成

导出的数据格式专为AI处理设计：
1. **标准化结构**: 统一的JSON格式
2. **完整路径**: 包含照片的完整路径信息
3. **元数据**: 包含团队信息和导出时间
4. **易于解析**: 结构清晰，便于程序处理

## 技术栈

- **框架**: Streamlit
- **图像处理**: Pillow (PIL)
- **数据处理**: Pandas, JSON
- **文件系统**: Python标准库

## 优势

1. **快速开发**: Streamlit让Web应用开发变得简单
2. **用户友好**: 直观的界面，无需技术背景
3. **功能完整**: 涵盖球队管理的所有核心需求
4. **易于扩展**: 模块化设计，便于添加新功能
5. **AI就绪**: 数据格式适合后续AI处理

这个Streamlit版本的球队管理系统提供了与Flask版本相同的功能，但具有更现代的界面和更简单的部署方式。
