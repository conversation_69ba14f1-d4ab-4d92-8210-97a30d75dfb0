#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
侧边栏组件
Sidebar Component

提供侧边栏相关的UI组件
"""

import streamlit as st
from typing import List, Tuple

from services.team_service import TeamService
from services.export_service import ExportService
from config.constants import UIConstants


class SidebarComponent:
    """侧边栏组件"""

    def __init__(self):
        self.team_service = TeamService()
        # 获取当前用户ID用于ExportService
        user_id = self._get_current_user_id()
        self.export_service = ExportService(user_id)

    def _get_current_user_id(self) -> str:
        """获取当前用户ID"""
        return st.session_state.get('user_id', '')
    
    def render_team_selector(self) -> str:
        """
        渲染球队选择器

        Returns:
            str: 选中的球队名称
        """
        st.header("球队管理")

        # 强制刷新球队列表（避免缓存问题）
        teams = self._get_fresh_teams_list()

        # 检查用户是否有球队
        if not teams or len(teams) == 0:
            # 新用户没有球队，显示引导信息
            st.info("👋 欢迎！请先创建您的第一个球队。")
            return None

        team_display_names = self.team_service.get_team_display_names(teams)

        # 获取当前选中的球队
        current_team = st.session_state.get('current_team', None)

        # 如果当前球队不在列表中，选择第一个
        if current_team not in teams:
            current_team = teams[0] if teams else None
            st.session_state.current_team = current_team

        current_display = self.team_service.get_team_display_name(current_team)

        # 确定默认选中的索引
        try:
            default_index = team_display_names.index(current_display)
        except (ValueError, AttributeError):
            default_index = 0

        # 球队选择器
        selected_display = st.selectbox(
            "选择球队",
            team_display_names,
            index=default_index,
            key="team_selector"
        )

        # 转换显示名称为实际名称
        if selected_display == UIConstants.DEFAULTS["team_display_name"]:
            selected_team = 'default'
        else:
            selected_team = selected_display

        # 检查是否切换了球队
        if selected_team != st.session_state.get('current_team'):
            st.session_state.current_team = selected_team
            # 球队切换时清除相关缓存
            self._clear_team_caches()

        return selected_team

    def _get_fresh_teams_list(self):
        """获取最新的球队列表，避免缓存问题"""
        try:
            # 使用无缓存的方法获取最新的球队列表
            teams = self.team_service.get_teams_list_fresh()
            return teams if teams else []
        except Exception as e:
            st.error(f"获取球队列表失败：{str(e)}")
            return []
    
    def render_team_creator(self) -> bool:
        """
        渲染球队创建器

        Returns:
            bool: 是否创建了新球队
        """
        st.subheader("创建新球队")

        # 使用唯一的key避免状态冲突
        new_team_name = st.text_input(
            "球队名称",
            placeholder="请输入球队名称",
            key="new_team_name_input"
        )

        # 创建球队按钮
        create_button = st.button(
            "创建球队",
            key="create_team_button",
            type="primary"
        )

        if create_button:
            if new_team_name and new_team_name.strip():
                team_name = new_team_name.strip()

                # 显示创建进度
                with st.spinner('🏗️ 正在创建球队...'):
                    try:
                        success, message = self.team_service.create_team(team_name)

                        if success:
                            # 成功创建球队
                            st.success(f"🎉 {message}")

                            # 立即更新session state
                            st.session_state.current_team = team_name

                            # 清除相关缓存
                            self._clear_team_caches()

                            # 显示成功提示
                            st.info("✅ 球队创建成功！正在切换到新球队...")

                            # 重新运行应用以刷新界面
                            st.rerun()
                            return True

                        else:
                            # 创建失败
                            st.error(f"❌ {message}")

                            # 提供具体的解决建议
                            if "已存在" in message:
                                st.info("💡 提示：球队名称已存在，请选择其他名称或在上方下拉菜单中选择已存在的球队")
                            elif "用户" in message:
                                st.info("💡 提示：请检查登录状态或重新登录")
                            else:
                                st.info("💡 提示：请检查球队名称是否符合要求（2-50个字符，不包含特殊符号）")

                    except Exception as e:
                        st.error(f"❌ 创建球队时发生错误：{str(e)}")
                        st.info("💡 请刷新页面重试，或联系管理员")

            else:
                st.error("❌ 请输入有效的球队名称")
                st.info("💡 球队名称不能为空，请输入2-50个字符")

        return False

    def _clear_team_caches(self):
        """清除球队相关的缓存"""
        try:
            # 清除Streamlit缓存
            st.cache_data.clear()

            # 清除可能的函数缓存
            if hasattr(self.team_service, 'get_teams_list'):
                if hasattr(self.team_service.get_teams_list, 'clear'):
                    self.team_service.get_teams_list.clear()

            if hasattr(self.team_service, 'get_user_teams'):
                if hasattr(self.team_service.get_user_teams, 'clear'):
                    self.team_service.get_user_teams.clear()

            # 清除session state中的缓存
            cache_keys_to_clear = [
                'teams_cache',
                'current_team_cache',
                'team_display_names_cache'
            ]

            for key in cache_keys_to_clear:
                if key in st.session_state:
                    del st.session_state[key]

        except Exception as e:
            # 缓存清除失败不应该影响主要功能
            pass
    
    def render_team_photo_processing_button(self) -> None:
        """渲染球队照片处理按钮"""
        st.subheader("🎨 球队照片处理")
        st.markdown("*为球队球员进行批量AI照片处理*")

        if st.button("🎨 球队照片处理", use_container_width=True):
            st.session_state.batch_mode = 'photo_process'
            st.session_state.show_add_form = False
            st.rerun()

        st.markdown("*批量处理球员照片，用于报名表生成*")
    
    def render_export_section(self, current_team: str) -> None:
        """
        渲染导出功能区域
        
        Args:
            current_team: 当前球队名称
        """
        st.subheader("📊 系统功能")
        st.markdown("*管理员功能*")
        
        if st.button("📥 导出数据", use_container_width=True, 
                    help="管理员功能：导出球队数据供后台处理"):
            
            export_data = self.export_service.export_team_data(current_team)
            filename = self.export_service.get_export_filename(current_team)
            json_string = self.export_service.export_to_json_string(current_team)
            
            st.download_button(
                label="下载JSON文件",
                data=json_string,
                file_name=filename,
                mime="application/json"
            )
        
        st.markdown("*用于后台数据处理和报名表生成*")
    
    def render_team_stats(self, current_team: str) -> None:
        """
        渲染球队统计信息
        
        Args:
            current_team: 当前球队名称
        """
        stats = self.team_service.get_team_stats(current_team)
        
        if stats['total_players'] > 0:
            st.markdown("### 📊 球队统计")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.metric("👥 球员总数", stats['total_players'])
            
            with col2:
                st.metric("📸 已上传照片", 
                         f"{stats['players_with_photos']}/{stats['total_players']}")
            
            # 进度条
            progress = stats['completion_rate'] / 100
            st.progress(progress)
            st.caption(f"完成度: {stats['completion_rate']:.1f}%")
            
            if stats['is_complete']:
                st.success("✅ 球队档案完整")
            else:
                missing = stats['total_players'] - stats['players_with_photos']
                st.warning(f"⚠️ 还有 {missing} 人未上传照片")
    
    def render_sidebar(self) -> str:
        """
        渲染完整的侧边栏
        
        Returns:
            str: 当前选中的球队名称
        """
        with st.sidebar:
            # 球队选择器
            current_team = self.render_team_selector()
            
            # 球队创建器
            self.render_team_creator()
            
            # 球队统计
            self.render_team_stats(current_team)
            
            # 球队照片处理按钮
            self.render_team_photo_processing_button()
            
            # 导出功能
            self.render_export_section(current_team)
        
        return current_team
