#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化AI对话逻辑的脚本 - 第二个文件夹版本
验证AI现在只需要收集3项基本信息：联系人姓名、联系电话、球衣颜色
"""

import os
import json
import sys
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simplified_validation():
    """测试简化的验证逻辑"""
    print("🧪 测试简化的验证逻辑")
    print("=" * 50)
    
    try:
        # 导入AI服务
        sys.path.append('streamlit_team_management_modular')
        from services.ai_service import AIService
        
        ai_service = AIService()
        
        # 测试1：只提供3项基本信息
        print("1. 测试只提供3项基本信息...")
        basic_info = {
            'contact_person': '张三',
            'contact_phone': '13800138000',
            'jersey_color': '红色'
        }
        
        missing = ai_service.validate_extracted_info(basic_info)
        if not missing:
            print("✅ 3项基本信息验证通过")
        else:
            print(f"❌ 验证失败，缺失：{missing}")
            return False
        
        # 测试2：缺少联系人
        print("\n2. 测试缺少联系人...")
        incomplete_info = {
            'contact_phone': '13800138000',
            'jersey_color': '红色'
        }
        
        missing = ai_service.validate_extracted_info(incomplete_info)
        if '联系人姓名' in missing:
            print("✅ 正确检测到缺少联系人姓名")
        else:
            print("❌ 未能检测到缺少联系人姓名")
            return False
        
        # 测试3：缺少电话
        print("\n3. 测试缺少电话...")
        incomplete_info = {
            'contact_person': '张三',
            'jersey_color': '红色'
        }
        
        missing = ai_service.validate_extracted_info(incomplete_info)
        if '联系电话' in missing:
            print("✅ 正确检测到缺少联系电话")
        else:
            print("❌ 未能检测到缺少联系电话")
            return False
        
        # 测试4：缺少球衣颜色
        print("\n4. 测试缺少球衣颜色...")
        incomplete_info = {
            'contact_person': '张三',
            'contact_phone': '13800138000'
        }
        
        missing = ai_service.validate_extracted_info(incomplete_info)
        if '球衣颜色' in missing:
            print("✅ 正确检测到缺少球衣颜色")
        else:
            print("❌ 未能检测到缺少球衣颜色")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_fill_logic():
    """测试智能填充逻辑"""
    print("\n🧪 测试智能填充逻辑")
    print("=" * 50)
    
    try:
        # 导入AI服务
        from services.ai_service import AIService
        
        ai_service = AIService()
        
        # 测试智能填充
        print("1. 测试智能填充逻辑...")
        basic_info = {
            'contact_person': '张三',
            'contact_phone': '13800138000',
            'jersey_color': '红色'
        }
        
        # 调用智能填充方法
        filled_info = ai_service._auto_fill_personnel(basic_info)
        filled_info = ai_service._auto_fill_kit_colors(filled_info)
        
        print("填充后的信息：")
        for key, value in filled_info.items():
            print(f"  {key}: {value}")
        
        # 验证智能填充结果
        expected_fills = {
            'leader_name': '张三',  # 应该自动填充为联系人
            'team_doctor': '张三',  # 应该自动填充为联系人
            'shorts_color': '黑色',  # 红色球衣配黑色球裤
            'socks_color': '红色',  # 球袜与球衣同色
            'goalkeeper_kit_color': '绿色'  # 守门员默认绿色
        }
        
        success = True
        for key, expected_value in expected_fills.items():
            actual_value = filled_info.get(key, '')
            if actual_value == expected_value:
                print(f"✅ {key}: {actual_value} (符合预期)")
            else:
                print(f"❌ {key}: {actual_value} (期望: {expected_value})")
                success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_initial_message():
    """测试AI初始消息"""
    print("\n🧪 测试AI初始消息")
    print("=" * 50)
    
    try:
        from services.ai_service import AIService
        
        ai_service = AIService()
        
        # 模拟球队统计信息
        team_stats = {
            'total_players': 5,
            'players_with_photos': 3,
            'completion_rate': 60.0,
            'is_complete': False
        }
        
        # 生成初始消息
        initial_message = ai_service._generate_initial_message("测试球队", team_stats)
        
        print("AI初始消息：")
        print("-" * 40)
        print(initial_message)
        print("-" * 40)
        
        # 检查关键词
        key_phrases = [
            "建立基本档案",
            "3项核心信息",
            "联系人姓名",
            "联系电话",
            "球衣主色调",
            "智能填充",
            "智能处理"
        ]
        
        found_phrases = []
        for phrase in key_phrases:
            if phrase in initial_message:
                found_phrases.append(phrase)
                print(f"✅ 包含关键词: {phrase}")
            else:
                print(f"❌ 缺少关键词: {phrase}")
        
        if len(found_phrases) >= 5:  # 至少包含5个关键词
            print("✅ AI初始消息符合简化逻辑")
            return True
        else:
            print("❌ AI初始消息不符合简化逻辑")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 简化AI对话逻辑测试 - 第二个文件夹")
    print("=" * 60)
    
    try:
        # 测试1：验证逻辑
        test1_result = test_simplified_validation()
        
        # 测试2：智能填充逻辑
        test2_result = test_smart_fill_logic()
        
        # 测试3：AI初始消息
        test3_result = test_ai_initial_message()
        
        # 总结
        print("\n📊 测试结果总结")
        print("=" * 50)
        print(f"简化验证逻辑: {'✅ 通过' if test1_result else '❌ 失败'}")
        print(f"智能填充逻辑: {'✅ 通过' if test2_result else '❌ 失败'}")
        print(f"AI初始消息: {'✅ 通过' if test3_result else '❌ 失败'}")
        
        if test1_result and test2_result and test3_result:
            print("\n🎉 所有测试通过！第二个文件夹AI对话逻辑已成功简化。")
            print("\n💡 简化效果:")
            print("1. 用户只需提供3项基本信息：联系人姓名、联系电话、球衣颜色")
            print("2. 领队和队医自动填充为联系人")
            print("3. 球裤、球袜、守门员服装颜色智能搭配")
            print("4. AI初始消息明确说明简化流程")
            print("5. 验证逻辑只检查3个必填字段")
            print("6. 两个文件夹现在完全一致")
        else:
            print("\n⚠️ 部分测试失败，需要进一步检查。")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
