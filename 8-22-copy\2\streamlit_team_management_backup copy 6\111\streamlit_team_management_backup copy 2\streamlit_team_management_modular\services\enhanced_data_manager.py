#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强数据管理器
兼容原始ai_data_collector_test的数据管理功能，但适配现有的用户隔离架构
"""

import json
import os
import uuid
import streamlit as st
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from pathlib import Path

from services.auth_service import AuthService
from services.team_service import TeamService
from services.player_service import PlayerService


class EnhancedDataManager:
    """增强数据管理器 - 兼容原始功能但适配现有架构"""
    
    def __init__(self, user_id: str = None):
        """
        初始化增强数据管理器
        
        Args:
            user_id: 用户ID，确保用户隔离
        """
        self.auth_service = AuthService()
        self.team_service = TeamService()
        self.player_service = PlayerService()
        
        # 获取用户ID
        self.user_id = user_id or self.auth_service.get_current_user_id()
        if not self.user_id:
            raise ValueError("用户未登录，无法初始化数据管理器")
        
        # 获取用户数据路径
        self.user_data_path = self.auth_service.get_user_data_path(self.user_id)
        self.enhanced_data_path = os.path.join(self.user_data_path, "enhanced_ai_data")
        
        # 确保目录存在
        os.makedirs(self.enhanced_data_path, exist_ok=True)
    
    def save_team_info(self, team_data: Dict, team_id: Optional[str] = None) -> Dict:
        """
        保存球队信息（兼容原始API）
        
        Args:
            team_data: 球队数据
            team_id: 球队ID（可选，兼容原始系统）
            
        Returns:
            Dict: 保存结果
        """
        try:
            # 提取球队名称
            team_name = team_data.get("basic_info", {}).get("team_name")
            if not team_name:
                return {
                    "success": False,
                    "message": "缺少球队名称",
                    "team_id": team_id
                }
            
            # 生成或使用现有的team_id
            if not team_id:
                team_id = str(uuid.uuid4())
            
            # 保存到增强数据文件
            enhanced_file = os.path.join(self.enhanced_data_path, f"team_{team_id}.json")
            
            # 加载现有数据或创建新数据
            if os.path.exists(enhanced_file):
                with open(enhanced_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            else:
                existing_data = {
                    "team_id": team_id,
                    "team_name": team_name,
                    "created_at": datetime.now().isoformat(),
                    "team_info": {},
                    "players": []
                }
            
            # 更新球队信息
            existing_data["team_info"].update(team_data)
            existing_data["updated_at"] = datetime.now().isoformat()
            
            # 保存增强数据
            with open(enhanced_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
            
            # 同时保存到现有系统（如果球队存在）
            try:
                self._sync_to_existing_system(team_name, team_data)
            except Exception as e:
                # 同步失败不影响主要功能
                print(f"同步到现有系统失败: {e}")
            
            # 保存到session state
            if "enhanced_teams" not in st.session_state:
                st.session_state.enhanced_teams = {}
            st.session_state.enhanced_teams[team_id] = existing_data
            
            return {
                "success": True,
                "message": "球队信息保存成功",
                "team_id": team_id,
                "team_name": team_name,
                "data": existing_data["team_info"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"保存失败: {str(e)}",
                "team_id": team_id
            }
    
    def save_player_info(self, player_data: Dict, team_id: str) -> Dict:
        """
        保存球员信息（兼容原始API）
        
        Args:
            player_data: 球员数据
            team_id: 球队ID
            
        Returns:
            Dict: 保存结果
        """
        try:
            enhanced_file = os.path.join(self.enhanced_data_path, f"team_{team_id}.json")
            
            if not os.path.exists(enhanced_file):
                return {
                    "success": False,
                    "message": "球队不存在，请先创建球队"
                }
            
            # 加载球队数据
            with open(enhanced_file, 'r', encoding='utf-8') as f:
                team_data_full = json.load(f)
            
            # 检查球衣号码是否冲突
            jersey_number = player_data.get("basic_info", {}).get("jersey_number")
            if jersey_number:
                for existing_player in team_data_full["players"]:
                    existing_number = existing_player.get("basic_info", {}).get("jersey_number")
                    if existing_number == jersey_number:
                        return {
                            "success": False,
                            "message": f"球衣号码 {jersey_number} 已被使用"
                        }
            
            # 添加球员ID和时间戳
            player_data["player_id"] = str(uuid.uuid4())
            player_data["created_at"] = datetime.now().isoformat()
            
            # 添加球员到列表
            team_data_full["players"].append(player_data)
            team_data_full["updated_at"] = datetime.now().isoformat()
            
            # 保存数据
            with open(enhanced_file, 'w', encoding='utf-8') as f:
                json.dump(team_data_full, f, ensure_ascii=False, indent=2)
            
            # 同步到现有系统
            try:
                self._sync_player_to_existing_system(team_data_full["team_name"], player_data)
            except Exception as e:
                print(f"同步球员到现有系统失败: {e}")
            
            # 更新session state
            if "enhanced_teams" not in st.session_state:
                st.session_state.enhanced_teams = {}
            st.session_state.enhanced_teams[team_id] = team_data_full
            
            return {
                "success": True,
                "message": "球员信息保存成功",
                "player_id": player_data["player_id"],
                "team_id": team_id,
                "data": player_data
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"保存球员信息失败: {str(e)}"
            }
    
    def get_team_info(self, team_id: str) -> Dict:
        """
        获取球队信息（兼容原始API）
        
        Args:
            team_id: 球队ID
            
        Returns:
            Dict: 球队信息
        """
        try:
            enhanced_file = os.path.join(self.enhanced_data_path, f"team_{team_id}.json")
            
            if not os.path.exists(enhanced_file):
                return {
                    "success": False,
                    "message": "球队不存在"
                }
            
            with open(enhanced_file, 'r', encoding='utf-8') as f:
                team_data = json.load(f)
            
            return {
                "success": True,
                "message": "获取球队信息成功",
                "team_id": team_id,
                "data": team_data
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"获取球队信息失败: {str(e)}"
            }
    
    def get_player_list(self, team_id: str) -> Dict:
        """
        获取球员列表（兼容原始API）
        
        Args:
            team_id: 球队ID
            
        Returns:
            Dict: 球员列表
        """
        try:
            team_info = self.get_team_info(team_id)
            if not team_info["success"]:
                return team_info
            
            players = team_info["data"].get("players", [])
            
            return {
                "success": True,
                "message": f"获取球员列表成功，共{len(players)}名球员",
                "team_id": team_id,
                "players": players,
                "count": len(players)
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"获取球员列表失败: {str(e)}"
            }
    
    def check_data_completeness(self, team_id: str) -> Dict:
        """
        检查数据完整性（兼容原始API）
        
        Args:
            team_id: 球队ID
            
        Returns:
            Dict: 完整性检查结果
        """
        try:
            team_info = self.get_team_info(team_id)
            if not team_info["success"]:
                return team_info
            
            team_data = team_info["data"]
            missing_fields = []
            warnings = []
            
            # 检查球队基本信息
            basic_info = team_data.get("team_info", {}).get("basic_info", {})
            required_fields = ["team_name", "contact_person", "contact_phone"]
            
            for field in required_fields:
                if not basic_info.get(field):
                    missing_fields.append(f"球队基本信息.{field}")
            
            # 检查球员信息
            players = team_data.get("players", [])
            if len(players) < 5:
                warnings.append(f"球员数量不足，当前{len(players)}人，建议至少5人")
            
            # 检查球衣号码
            jersey_numbers = []
            for player in players:
                number = player.get("basic_info", {}).get("jersey_number")
                if number:
                    if number in jersey_numbers:
                        warnings.append(f"球衣号码{number}重复")
                    jersey_numbers.append(number)
            
            completeness_score = max(0, 100 - len(missing_fields) * 20 - len(warnings) * 10)
            
            return {
                "success": True,
                "message": "数据完整性检查完成",
                "team_id": team_id,
                "completeness_score": completeness_score,
                "missing_fields": missing_fields,
                "warnings": warnings,
                "players_count": len(players)
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"数据完整性检查失败: {str(e)}"
            }
    
    def validate_jersey_number(self, team_id: str, jersey_number: int) -> Dict:
        """
        验证球衣号码（兼容原始API）
        
        Args:
            team_id: 球队ID
            jersey_number: 球衣号码
            
        Returns:
            Dict: 验证结果
        """
        try:
            player_list = self.get_player_list(team_id)
            if not player_list["success"]:
                return player_list
            
            # 检查号码是否已被使用
            for player in player_list["players"]:
                existing_number = player.get("basic_info", {}).get("jersey_number")
                if str(existing_number) == str(jersey_number):
                    return {
                        "success": False,
                        "message": f"球衣号码{jersey_number}已被{player.get('basic_info', {}).get('name', '未知球员')}使用",
                        "available": False,
                        "jersey_number": jersey_number
                    }
            
            return {
                "success": True,
                "message": f"球衣号码{jersey_number}可用",
                "available": True,
                "jersey_number": jersey_number
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"验证球衣号码失败: {str(e)}"
            }
    
    def _sync_to_existing_system(self, team_name: str, team_data: Dict):
        """同步到现有系统"""
        try:
            # 这里可以添加同步逻辑
            # 目前只保存到session state
            if "team_sync_data" not in st.session_state:
                st.session_state.team_sync_data = {}
            st.session_state.team_sync_data[team_name] = team_data
        except Exception as e:
            print(f"同步失败: {e}")
    
    def _sync_player_to_existing_system(self, team_name: str, player_data: Dict):
        """同步球员到现有系统"""
        try:
            # 这里可以添加球员同步逻辑
            if "player_sync_data" not in st.session_state:
                st.session_state.player_sync_data = {}
            if team_name not in st.session_state.player_sync_data:
                st.session_state.player_sync_data[team_name] = []
            st.session_state.player_sync_data[team_name].append(player_data)
        except Exception as e:
            print(f"同步球员失败: {e}")
    
    def list_teams(self) -> Dict:
        """列出所有球队"""
        try:
            teams = []
            for file_name in os.listdir(self.enhanced_data_path):
                if file_name.startswith("team_") and file_name.endswith(".json"):
                    team_id = file_name[5:-5]  # 移除 "team_" 前缀和 ".json" 后缀
                    team_info = self.get_team_info(team_id)
                    if team_info["success"]:
                        teams.append({
                            "team_id": team_id,
                            "team_name": team_info["data"].get("team_name", "未知"),
                            "created_at": team_info["data"].get("created_at"),
                            "players_count": len(team_info["data"].get("players", []))
                        })
            
            return {
                "success": True,
                "message": f"获取球队列表成功，共{len(teams)}支球队",
                "teams": teams,
                "count": len(teams)
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"获取球队列表失败: {str(e)}"
            }
