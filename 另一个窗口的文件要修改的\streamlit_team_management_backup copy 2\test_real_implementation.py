#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实环境测试修改后的代码
Real Environment Test for Modified Code

测试AI换装后自动Word生成的实际实现
"""

import os
import sys
import tempfile
import json
from PIL import Image
import traceback

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

def create_real_test_image():
    """创建真实的测试图片"""
    # 创建一个简单的人物轮廓图片
    img = Image.new('RGB', (400, 600), (255, 255, 255))  # 白色背景
    
    # 简单绘制一个人物轮廓（用于测试）
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    
    # 头部
    draw.ellipse([150, 50, 250, 150], fill=(255, 220, 177))  # 肤色
    
    # 身体
    draw.rectangle([175, 150, 225, 350], fill=(0, 0, 255))  # 蓝色衣服
    
    # 腿部
    draw.rectangle([175, 350, 200, 500], fill=(0, 0, 0))  # 黑色裤子
    draw.rectangle([200, 350, 225, 500], fill=(0, 0, 0))
    
    return img

def test_image_processing_request_with_team_context():
    """测试ImageProcessingRequest的team_context功能"""
    print("🧪 测试ImageProcessingRequest的team_context功能...")
    
    try:
        from models.image_processing import ImageProcessingRequest, ProcessingType
        
        # 创建带team_context的请求
        team_context = {
            'team_name': '真实测试足球队',
            'organization': '测试组织',
            'leader': '张三',
            'current_player': {
                'name': '真实测试球员',
                'number': '10'
            }
        }
        
        request = ImageProcessingRequest(
            source_image_path="test_image.jpg",
            processing_types=[ProcessingType.FASHION_TRYON],
            team_context=team_context
        )
        
        print(f"✅ 请求创建成功")
        print(f"   球队名称: {request.team_context['team_name']}")
        print(f"   球员姓名: {request.team_context['current_player']['name']}")
        
        # 测试序列化和反序列化
        request_dict = request.to_dict()
        print(f"✅ 序列化成功")
        
        restored_request = ImageProcessingRequest.from_dict(request_dict)
        print(f"✅ 反序列化成功")
        print(f"   恢复的球队名称: {restored_request.team_context['team_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False

def test_ai_image_engine_trigger_logic():
    """测试AI图像引擎的触发逻辑"""
    print("\n🧪 测试AI图像引擎的触发逻辑...")
    
    try:
        from services.ai_image_engine import AIImageEngine
        from models.image_processing import ImageProcessingRequest, ProcessingType
        
        engine = AIImageEngine()
        
        # 创建测试图片
        test_img = create_real_test_image()
        temp_img_path = os.path.join(tempfile.gettempdir(), "real_test_player.jpg")
        test_img.save(temp_img_path, 'JPEG')
        print(f"✅ 测试图片创建: {temp_img_path}")
        
        # 测试触发逻辑的方法是否存在
        if hasattr(engine, '_trigger_auto_word_generation'):
            print("✅ _trigger_auto_word_generation方法存在")
        else:
            print("❌ _trigger_auto_word_generation方法不存在")
            return False
        
        # 测试准备数据的方法是否存在
        if hasattr(engine, '_prepare_team_data_for_word'):
            print("✅ _prepare_team_data_for_word方法存在")
        else:
            print("❌ _prepare_team_data_for_word方法不存在")
            return False
        
        if hasattr(engine, '_prepare_players_data_for_word'):
            print("✅ _prepare_players_data_for_word方法存在")
        else:
            print("❌ _prepare_players_data_for_word方法不存在")
            return False
        
        # 测试方法调用（不实际执行Word生成）
        team_context = {
            'team_name': '测试队',
            'current_player': {'name': '球员', 'number': '10'}
        }
        
        team_data = engine._prepare_team_data_for_word(team_context)
        print(f"✅ 球队数据准备成功: {team_data}")
        
        players_data = engine._prepare_players_data_for_word(team_context, temp_img_path)
        print(f"✅ 球员数据准备成功: {len(players_data)} 个球员")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            if 'temp_img_path' in locals() and os.path.exists(temp_img_path):
                os.remove(temp_img_path)
        except:
            pass

def test_photo_service_new_method():
    """测试PhotoService的新方法"""
    print("\n🧪 测试PhotoService的新方法...")
    
    try:
        from services.photo_service import PhotoService
        
        service = PhotoService()
        
        # 检查新方法是否存在
        if hasattr(service, 'process_photos_with_auto_word'):
            print("✅ process_photos_with_auto_word方法存在")
        else:
            print("❌ process_photos_with_auto_word方法不存在")
            return False
        
        if hasattr(service, '_get_processing_types_from_option'):
            print("✅ _get_processing_types_from_option方法存在")
        else:
            print("❌ _get_processing_types_from_option方法不存在")
            return False
        
        # 测试处理类型映射
        from models.image_processing import ProcessingType
        
        test_options = ["换装", "背景去除", "添加白底", "全套处理"]
        for option in test_options:
            types = service._get_processing_types_from_option(option)
            print(f"   {option} → {[t.value for t in types]}")
        
        print("✅ 处理类型映射测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False

def test_word_generator_service_real():
    """测试Word生成服务的真实调用"""
    print("\n🧪 测试Word生成服务的真实调用...")
    
    try:
        from word_generator_service import create_word_generator_service
        
        # 创建服务
        word_service = create_word_generator_service()
        print("✅ Word生成服务创建成功")
        
        # 创建真实的测试图片
        test_img = create_real_test_image()
        temp_img_path = os.path.join(tempfile.gettempdir(), "real_word_test.jpg")
        test_img.save(temp_img_path, 'JPEG')
        print(f"✅ 测试图片创建: {temp_img_path}")
        
        # 准备真实的测试数据
        team_data = {
            'name': '真实测试足球队',
            'organization': '测试组织',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'contact': '13800138000'
        }
        
        players_data = [
            {
                'name': '真实测试球员1',
                'jersey_number': '10',
                'photo': temp_img_path
            },
            {
                'name': '真实测试球员2',
                'jersey_number': '9',
                'photo': temp_img_path
            }
        ]
        
        print("✅ 测试数据准备完成")
        print(f"   球队: {team_data['name']}")
        print(f"   球员数量: {len(players_data)}")
        
        # 尝试生成Word文档
        print("📄 开始生成Word文档...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print("✅ Word文档生成成功!")
            print(f"   文件路径: {result['file_path']}")
            
            # 检查文件是否真实存在
            if os.path.exists(result['file_path']):
                file_size = os.path.getsize(result['file_path'])
                print(f"   文件大小: {file_size} 字节")
                
                # 检查是否有裁剪后的图片
                photos_dir = os.path.join("word_zc", "ai-football-generator", "photos")
                if os.path.exists(photos_dir):
                    cropped_files = [f for f in os.listdir(photos_dir) if 'cropped' in f]
                    print(f"   裁剪图片数量: {len(cropped_files)}")
                    for f in cropped_files[-3:]:  # 显示最新的3个
                        print(f"     - {f}")
                
                return True
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print(f"❌ Word文档生成失败: {result.get('message', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            if 'temp_img_path' in locals() and os.path.exists(temp_img_path):
                os.remove(temp_img_path)
        except:
            pass

def test_config_import():
    """测试配置导入"""
    print("\n🧪 测试配置导入...")
    
    try:
        from config import WORD_CONFIG
        print("✅ WORD_CONFIG导入成功")
        print(f"   JAR路径: {WORD_CONFIG.get('jar_path')}")
        print(f"   模板路径: {WORD_CONFIG.get('template_path')}")
        print(f"   输出目录: {WORD_CONFIG.get('output_dir')}")
        
        # 检查实际文件是否存在
        jar_path = WORD_CONFIG.get('jar_path')
        if jar_path and os.path.exists(jar_path):
            print(f"✅ JAR文件存在: {jar_path}")
        else:
            print(f"⚠️ JAR文件不存在或路径错误: {jar_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置导入失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始真实环境测试修改后的代码")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("配置导入测试", test_config_import),
        ("ImageProcessingRequest team_context测试", test_image_processing_request_with_team_context),
        ("AI图像引擎触发逻辑测试", test_ai_image_engine_trigger_logic),
        ("PhotoService新方法测试", test_photo_service_new_method),
        ("Word生成服务真实调用测试", test_word_generator_service_real)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 真实环境测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有真实环境测试通过！")
        print("\n💡 验证的功能:")
        print("   ✅ ImageProcessingRequest的team_context字段正常工作")
        print("   ✅ AI图像引擎的自动Word生成触发逻辑已实现")
        print("   ✅ PhotoService的新方法正常工作")
        print("   ✅ Word生成服务能够真实生成文档")
        print("   ✅ Java端的裁剪功能正常工作")
        
        print("\n🎯 实现确认:")
        print("   ✅ AI换装后自动Word生成功能已成功实现")
        print("   ✅ Java端先裁剪再生成Word的逻辑保持不变")
        print("   ✅ Python端智能触发机制工作正常")
        print("   ✅ 球队上下文信息传递正确")
    else:
        print(f"\n⚠️ 发现 {total-passed} 个问题需要修复")
        
        failed_tests = [name for name, success in results if not success]
        print("\n❌ 失败的测试:")
        for test_name in failed_tests:
            print(f"   - {test_name}")
        
        print("\n💡 可能的问题:")
        print("   - 依赖模块缺失或版本不兼容")
        print("   - 文件路径配置错误")
        print("   - Java环境配置问题")
        print("   - 代码逻辑错误")
    
    return passed == total

if __name__ == "__main__":
    main()
