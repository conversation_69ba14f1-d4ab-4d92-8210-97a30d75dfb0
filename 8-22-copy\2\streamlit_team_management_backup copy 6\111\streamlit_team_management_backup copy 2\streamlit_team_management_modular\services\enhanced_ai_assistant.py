#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的AI助手 - 处理复杂的用户输入场景
基于原始ai_data_collector_release的设计
"""

import json
import re
from typing import Dict, List, Optional, Any
from openai import OpenAI

from config.settings import app_settings
from config.ai_schemas import FUNCTION_DEFINITIONS, TEAM_INFO_SCHEMA
from services.field_metadata import field_metadata_manager, FieldSource, FieldStatus


class EnhancedAIAssistant:
    """增强的AI助手，支持复杂的用户交互场景"""
    
    def __init__(self):
        # 从streamlit secrets获取API密钥
        try:
            import streamlit as st
            api_key = st.secrets.get("OPENAI_API_KEY") if hasattr(st, 'secrets') else None
            self.client = OpenAI(api_key=api_key) if api_key else None
        except:
            self.client = None

        self.model = app_settings.ai.OPENAI_MODEL
        self.conversation_context = {}
        self.pending_confirmations = {}
        self.current_team_id = None
        
    def is_available(self) -> bool:
        """检查AI服务是否可用"""
        return self.client is not None
    
    def process_complex_message(self, message: str, team_id: str = None) -> str:
        """处理复杂的用户消息"""
        if not self.is_available():
            return "AI服务不可用，请检查配置"
        
        self.current_team_id = team_id
        
        # 1. 分析用户意图
        intent = self._analyze_user_intent(message)
        
        # 2. 根据意图处理
        if intent["type"] == "correction":
            return self._handle_correction(message, intent)
        elif intent["type"] == "update":
            return self._handle_update(message, intent)
        elif intent["type"] == "confirmation":
            return self._handle_confirmation(message, intent)
        elif intent["type"] == "deletion":
            return self._handle_deletion(message, intent)
        elif intent["type"] == "restart":
            return self._handle_restart(message, intent)
        else:
            # 使用基础助手处理
            return self._process_normal_message(message)
    
    def _analyze_user_intent(self, message: str) -> Dict:
        """分析用户意图"""
        message_lower = message.lower()
        
        # 修正意图关键词
        correction_keywords = ["不对", "错了", "改成", "修改", "更正", "应该是", "改为"]
        update_keywords = ["更新", "换成", "变更", "调整"]
        confirmation_keywords = ["对的", "正确", "确认", "没错", "是的"]
        deletion_keywords = ["删除", "不要", "去掉", "清空", "移除"]
        restart_keywords = ["重新开始", "重来", "重新", "清空所有"]
        
        intent = {"type": "normal", "confidence": 0.0, "keywords": []}
        
        # 检测修正意图
        for keyword in correction_keywords:
            if keyword in message_lower:
                intent["type"] = "correction"
                intent["confidence"] += 0.3
                intent["keywords"].append(keyword)
        
        # 检测更新意图
        for keyword in update_keywords:
            if keyword in message_lower:
                intent["type"] = "update"
                intent["confidence"] += 0.2
                intent["keywords"].append(keyword)
        
        # 检测确认意图
        for keyword in confirmation_keywords:
            if keyword in message_lower:
                intent["type"] = "confirmation"
                intent["confidence"] += 0.4
                intent["keywords"].append(keyword)
        
        # 检测删除意图
        for keyword in deletion_keywords:
            if keyword in message_lower:
                intent["type"] = "deletion"
                intent["confidence"] += 0.3
                intent["keywords"].append(keyword)
        
        # 检测重新开始意图
        for keyword in restart_keywords:
            if keyword in message_lower:
                intent["type"] = "restart"
                intent["confidence"] += 0.5
                intent["keywords"].append(keyword)
        
        return intent
    
    def _process_normal_message(self, message: str) -> str:
        """处理普通消息"""
        try:
            # 构建系统提示词
            system_prompt = self._build_system_prompt()
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": message}
            ]
            
            # 使用Function Calling
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                functions=FUNCTION_DEFINITIONS,
                function_call="auto"
            )
            
            # 处理响应
            return self._handle_response(response, message)
            
        except Exception as e:
            return f"处理消息时出错: {str(e)}"
    
    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        return """你是一个专业的足球队信息收集助手。你的任务是帮助用户收集完整的球队和球员信息，用于生成报名表。

重要说明：
1. 必填字段：球队名称、联系人、联系电话、领队姓名、队医姓名、球衣颜色
2. 可选字段：球裤颜色、球袜颜色、守门员服装颜色、教练姓名
3. 智能填充：如果只提到一个人名，可以智能填充到联系人、领队、队医等多个角色
4. 服装智能填充：根据球衣颜色自动推荐其他服装颜色

你需要：
1. 使用Function Calling保存和查询数据
2. 智能引导用户补充缺失信息
3. 验证数据的准确性和完整性
4. 提供友好的用户体验

请用友好、专业的语气与用户交流，一步步收集所需信息。"""
    
    def _handle_response(self, response, original_message: str) -> str:
        """处理AI响应"""
        message = response.choices[0].message
        
        # 检查是否有函数调用
        if message.function_call:
            function_result = self._execute_function_call(message.function_call)
            
            # 构建包含函数结果的新消息
            messages = [
                {"role": "system", "content": self._build_system_prompt()},
                {"role": "user", "content": original_message},
                {"role": "assistant", "content": None, "function_call": message.function_call},
                {"role": "function", "name": message.function_call.name, "content": json.dumps(function_result, ensure_ascii=False)}
            ]
            
            # 获取最终响应
            final_response = self.client.chat.completions.create(
                model=self.model,
                messages=messages
            )
            
            return final_response.choices[0].message.content
        else:
            return message.content
    
    def _execute_function_call(self, function_call) -> Dict:
        """执行函数调用"""
        function_name = function_call.name
        arguments = json.loads(function_call.arguments)
        
        if function_name == "save_team_info":
            return self._save_team_info(arguments)
        elif function_name == "get_team_info":
            return self._get_team_info(arguments)
        elif function_name == "extract_team_info_from_text":
            return self._extract_team_info_from_text(arguments)
        else:
            return {"success": False, "message": f"未知函数: {function_name}"}
    
    def _save_team_info(self, arguments: Dict) -> Dict:
        """保存球队信息"""
        try:
            team_data = arguments.get("team_data", {})
            team_id = arguments.get("team_id", self.current_team_id)
            
            # 应用智能填充逻辑
            if "basic_info" in team_data:
                team_data["basic_info"] = self._auto_fill_personnel(team_data["basic_info"])
            
            if "kit_colors" in team_data:
                team_data["kit_colors"] = self._auto_fill_kit_colors(team_data["kit_colors"])
            
            # 添加组织信息
            if "organization" not in team_data:
                team_data["organization"] = {
                    "seal_required": True,
                    "seal_notes": "此处需要在打印后加盖单位公章"
                }
            
            # 记录字段元数据
            self._record_field_metadata(team_id or "temp", team_data)

            # 🎨 自动生成队徽逻辑
            auto_logo_result = self._auto_generate_logo_on_save(team_data)
            if auto_logo_result:
                team_data["auto_generated_logo"] = auto_logo_result

            return {
                "success": True,
                "message": "球队信息保存成功" + ("，已自动生成队徽" if auto_logo_result else ""),
                "team_id": team_id,
                "data": team_data
            }
            
        except Exception as e:
            return {"success": False, "message": f"保存失败: {str(e)}"}

    def _auto_generate_logo_on_save(self, team_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """在保存球队信息时自动生成队徽"""
        try:
            # 从球队数据中提取必要信息
            basic_info = team_data.get("basic_info", {})
            kit_colors = team_data.get("kit_colors", {})

            team_name = basic_info.get("team_name", "")
            jersey_color = kit_colors.get("jersey_color", "")

            # 检查是否有足够信息生成队徽
            if not team_name or team_name.strip() == "":
                return None

            # 如果没有球衣颜色，使用默认颜色
            if not jersey_color or jersey_color.strip() == "":
                jersey_color = "蓝色"

            print(f"🎨 保存时自动生成队徽：球队={team_name}, 颜色={jersey_color}")

            # 调用AI服务生成队徽
            from services.enhanced_ai_service import enhanced_ai_service

            if enhanced_ai_service and enhanced_ai_service.is_available():
                logo_arguments = {
                    "team_name": team_name,
                    "team_style": "现代",
                    "color_preference": jersey_color
                }

                logo_result = enhanced_ai_service._generate_team_logo(logo_arguments)

                if logo_result and logo_result.get("success"):
                    print(f"✅ 保存时队徽生成成功：{logo_result.get('message', '')}")
                    return {
                        "generated": True,
                        "team_name": team_name,
                        "logo_description": logo_result.get("logo_description", ""),
                        "auto_trigger": "team_save"
                    }
                else:
                    print(f"❌ 保存时队徽生成失败：{logo_result.get('error', '未知错误')}")
                    return None
            else:
                print("⚠️ AI服务不可用，无法自动生成队徽")
                return None

        except Exception as e:
            print(f"❌ 保存时自动生成队徽异常: {e}")
            return None

    def _get_team_info(self, arguments: Dict) -> Dict:
        """获取球队信息"""
        team_id = arguments.get("team_id", self.current_team_id)
        
        if not team_id:
            return {"success": False, "message": "请先创建或选择球队"}
        
        # 这里应该从实际数据库获取，暂时返回模拟数据
        return {
            "success": True,
            "data": {},
            "message": "球队信息获取成功"
        }
    
    def _extract_team_info_from_text(self, arguments: Dict) -> Dict:
        """从文本中提取球队信息"""
        user_text = arguments.get("user_text", "")
        
        # 使用专业的信息提取提示词
        extraction_prompt = f"""
请从以下用户输入中提取球队信息。

必填字段：
1. 单位名称（球队名称）
2. 球队联系人
3. 联系电话
4. 领队姓名
5. 队医姓名
6. 球衣颜色（必填）

可选字段：
- 球裤颜色（可选，AI可自动填充）
- 球袜颜色（可选，AI可自动填充）
- 守门员服装颜色（可选，AI可自动填充）
- 教练姓名

注意：如果用户只提到一个人的名字，可以智能填充到联系人、领队、队医等多个角色中。

用户输入的文本：
{user_text}

请以JSON格式返回提取的信息，包含置信度评估。
"""
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是专业的信息提取助手，请准确提取用户输入中的球队信息。"},
                    {"role": "user", "content": extraction_prompt}
                ]
            )
            
            # 解析提取结果
            extracted_text = response.choices[0].message.content
            
            return {
                "success": True,
                "extracted_info": extracted_text,
                "confidence": 0.8
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"信息提取失败: {str(e)}"
            }
    
    def _auto_fill_personnel(self, basic_info: Dict) -> Dict:
        """智能填充人员信息"""
        # 收集所有提到的人名
        mentioned_names = []
        for field in ["contact_person", "leader_name", "team_doctor"]:
            name = basic_info.get(field)
            if name and isinstance(name, str):
                name = name.strip()
                if name and name not in mentioned_names:
                    mentioned_names.append(name)
        
        # 如果只提到一个人名，智能填充
        if len(mentioned_names) == 1:
            single_name = mentioned_names[0]
            
            if not basic_info.get("contact_person"):
                basic_info["contact_person"] = single_name
            
            if not basic_info.get("leader_name"):
                basic_info["leader_name"] = single_name
            
            if not basic_info.get("team_doctor"):
                basic_info["team_doctor"] = single_name
        
        return basic_info
    
    def _auto_fill_kit_colors(self, kit_colors: Dict) -> Dict:
        """智能填充服装颜色"""
        jersey_color = kit_colors.get("jersey_color", "").strip().lower()
        
        if jersey_color and not kit_colors.get("shorts_color"):
            if jersey_color in ["红", "蓝", "绿", "黄", "红色", "蓝色", "绿色", "黄色"]:
                kit_colors["shorts_color"] = "黑色"
            elif jersey_color in ["白", "白色"]:
                kit_colors["shorts_color"] = "白色"
            else:
                kit_colors["shorts_color"] = "黑色"
        
        if jersey_color and not kit_colors.get("socks_color"):
            kit_colors["socks_color"] = kit_colors.get("jersey_color", "")
        
        if jersey_color and not kit_colors.get("goalkeeper_kit_color"):
            if jersey_color in ["绿", "绿色"]:
                kit_colors["goalkeeper_kit_color"] = "橙色"
            elif jersey_color in ["白", "白色"]:
                kit_colors["goalkeeper_kit_color"] = "黄色"
            else:
                kit_colors["goalkeeper_kit_color"] = "绿色"
        
        return kit_colors
    
    def _record_field_metadata(self, team_id: str, team_data: Dict):
        """记录字段元数据"""
        # 记录基本信息字段
        if "basic_info" in team_data:
            for field, value in team_data["basic_info"].items():
                if value:
                    field_metadata_manager.update_field_with_metadata(
                        team_id, f"basic_info.{field}", value, FieldSource.USER_DIRECT
                    )
        
        # 记录服装颜色字段
        if "kit_colors" in team_data:
            for field, value in team_data["kit_colors"].items():
                if value:
                    source = FieldSource.AI_AUTO_COLOR if field != "jersey_color" else FieldSource.USER_DIRECT
                    field_metadata_manager.update_field_with_metadata(
                        team_id, f"kit_colors.{field}", value, source
                    )
    
    def _handle_correction(self, message: str, intent: Dict) -> str:
        """处理修正请求"""
        return "我理解您要修正信息。请告诉我具体要修改什么内容？"
    
    def _handle_update(self, message: str, intent: Dict) -> str:
        """处理更新请求"""
        return "我理解您要更新信息。请告诉我具体要更新什么内容？"
    
    def _handle_confirmation(self, message: str, intent: Dict) -> str:
        """处理确认请求"""
        return "好的，我已确认您的信息。"
    
    def _handle_deletion(self, message: str, intent: Dict) -> str:
        """处理删除请求"""
        return "我理解您要删除某些信息。请告诉我具体要删除什么？"
    
    def _handle_restart(self, message: str, intent: Dict) -> str:
        """处理重新开始请求"""
        return "好的，让我们重新开始收集球队信息。请告诉我您的球队名称。"


# 创建全局实例
enhanced_ai_assistant = EnhancedAIAssistant()
