#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立图片编辑器组件
Standalone Image Editor Component

提供独立的AI修图功能，不依赖球队管理数据
"""

import os
import streamlit as st
from typing import Optional, List
from PIL import Image

from models.image_processing import ImageProcessingRequest, ProcessingType, ProcessingSession
from services.ai_image_engine import AIImageEngine
from utils.image_utils import ImageProcessor
from config.settings import app_settings


class StandaloneImageEditorComponent:
    """独立图片编辑器组件"""
    
    def __init__(self):
        self.ai_engine = AIImageEngine()
        self.temp_upload_folder = os.path.join(app_settings.paths.UPLOAD_FOLDER, "temp_editor")
        self.ensure_temp_folder()
    
    def ensure_temp_folder(self) -> None:
        """确保临时文件夹存在"""
        if not os.path.exists(self.temp_upload_folder):
            os.makedirs(self.temp_upload_folder)
    
    def render_editor_interface(self) -> None:
        """渲染编辑器界面"""
        st.title("🎨 AI图片编辑器")
        st.markdown("**快速、智能的图片处理工具**")
        st.markdown("---")
        
        # 初始化会话状态
        if 'editor_session' not in st.session_state:
            st.session_state.editor_session = ProcessingSession()
        
        # 渲染主要功能区域
        self.render_upload_section()
        
        # 如果有上传的图片，显示处理选项
        if 'uploaded_image_path' in st.session_state and st.session_state.uploaded_image_path:
            st.markdown("---")
            self.render_processing_options()
            
        # 如果有处理结果，显示结果
        if 'processing_result' in st.session_state and st.session_state.processing_result:
            st.markdown("---")
            self.render_result_section()
    
    def render_upload_section(self) -> None:
        """渲染上传区域"""
        st.subheader("📤 上传图片")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            uploaded_file = st.file_uploader(
                "选择要处理的图片",
                type=['png', 'jpg', 'jpeg'],
                help="支持 PNG, JPG, JPEG 格式，建议图片大小不超过10MB"
            )
            
            if uploaded_file is not None:
                # 保存上传的文件
                file_path = self.save_uploaded_file(uploaded_file)
                if file_path:
                    st.session_state.uploaded_image_path = file_path
                    st.success("✅ 图片上传成功！")
        
        with col2:
            if 'uploaded_image_path' in st.session_state and st.session_state.uploaded_image_path:
                if os.path.exists(st.session_state.uploaded_image_path):
                    st.image(st.session_state.uploaded_image_path, caption="上传的图片", width=200)
                    
                    # 重新上传按钮
                    if st.button("🔄 重新上传"):
                        self.clear_session_data()
                        st.rerun()
    
    def render_processing_options(self) -> None:
        """渲染处理选项"""
        st.subheader("⚙️ 选择处理方式")
        
        # 获取处理选项
        options = self.ai_engine.get_processing_options()
        
        # 处理选项选择
        col1, col2 = st.columns([2, 1])
        
        with col1:
            selected_options = []
            
            for option_key, option_info in options.items():
                if st.checkbox(
                    option_info["label"],
                    help=option_info["description"],
                    key=f"option_{option_key}"
                ):
                    selected_options.append(option_key)
            
            # 模板图上传（如果需要换装）
            template_path = None
            if "换装" in selected_options:
                st.markdown("#### 🎽 上传模板图")
                template_file = st.file_uploader(
                    "选择服装模板图片",
                    type=['png', 'jpg', 'jpeg'],
                    help="用于AI换装的参考图片",
                    key="template_uploader"
                )
                
                if template_file is not None:
                    template_path = self.save_template_file(template_file)
                    if template_path:
                        st.image(template_path, caption="模板图片", width=200)
        
        with col2:
            st.markdown("#### 💡 处理说明")
            
            if not selected_options:
                st.info("请选择至少一种处理方式")
            else:
                for option in selected_options:
                    option_info = options[option]
                    st.markdown(f"• **{option_info['label']}**: {option_info['description']}")
                
                # 处理按钮
                if st.button("🚀 开始处理", type="primary", use_container_width=True):
                    self.start_processing(selected_options, template_path)
    
    def start_processing(self, selected_options: List[str], template_path: Optional[str]) -> None:
        """开始处理图片"""
        if not selected_options:
            st.error("请选择至少一种处理方式")
            return
        
        # 检查换装是否需要模板图
        if "换装" in selected_options and not template_path:
            st.error("换装处理需要上传模板图")
            return
        
        # 创建处理请求
        processing_types = []
        options = self.ai_engine.get_processing_options()
        
        for option in selected_options:
            if option in options:
                processing_types.append(options[option]["processing_type"])
        
        request = ImageProcessingRequest(
            source_image_path=st.session_state.uploaded_image_path,
            template_image_path=template_path,
            processing_types=processing_types
        )
        
        # 显示处理进度
        with st.spinner("🎨 AI正在处理您的图片，请稍候..."):
            # 执行处理
            result = self.ai_engine.process_image(request)
            
            # 保存结果到会话
            st.session_state.editor_session.add_request(request)
            st.session_state.editor_session.add_result(result)
            st.session_state.processing_result = result
        
        if result.success:
            st.success("✅ 图片处理完成！")
            st.rerun()
        else:
            st.error(f"❌ 处理失败: {result.error_message}")
    
    def render_result_section(self) -> None:
        """渲染结果区域"""
        result = st.session_state.processing_result
        
        if not result.success:
            st.error(f"处理失败: {result.error_message}")
            return
        
        st.subheader("✨ 处理结果")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 原图")
            if os.path.exists(st.session_state.uploaded_image_path):
                st.image(st.session_state.uploaded_image_path, caption="原始图片")
        
        with col2:
            st.markdown("#### 处理后")
            if result.processed_image_path and os.path.exists(result.processed_image_path):
                st.image(result.processed_image_path, caption="处理后图片")
                
                # 下载按钮
                with open(result.processed_image_path, "rb") as file:
                    st.download_button(
                        label="📥 下载处理后的图片",
                        data=file.read(),
                        file_name=f"processed_image_{result.request_id[:8]}.jpg",
                        mime="image/jpeg",
                        use_container_width=True
                    )
        
        # 处理信息
        st.markdown("#### 📊 处理信息")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("处理时间", f"{result.processing_time:.2f}秒")
        
        with col2:
            st.metric("处理状态", "成功" if result.success else "失败")
        
        with col3:
            if st.button("🔄 处理新图片"):
                self.clear_session_data()
                st.rerun()
    
    def save_uploaded_file(self, uploaded_file) -> Optional[str]:
        """保存上传的文件"""
        try:
            # 生成唯一文件名
            filename = ImageProcessor.generate_unique_filename(uploaded_file.name)
            file_path = os.path.join(self.temp_upload_folder, filename)
            
            # 处理并保存图片
            if ImageProcessor.process_uploaded_image(uploaded_file, file_path):
                return file_path
            else:
                st.error("图片保存失败")
                return None
                
        except Exception as e:
            st.error(f"保存文件失败: {e}")
            return None
    
    def save_template_file(self, template_file) -> Optional[str]:
        """保存模板文件"""
        try:
            filename = f"template_{ImageProcessor.generate_unique_filename(template_file.name)}"
            file_path = os.path.join(self.temp_upload_folder, filename)
            
            # 保存模板图片
            with open(file_path, "wb") as f:
                f.write(template_file.getvalue())
            
            return file_path
            
        except Exception as e:
            st.error(f"保存模板文件失败: {e}")
            return None
    
    def clear_session_data(self) -> None:
        """清理会话数据"""
        keys_to_clear = [
            'uploaded_image_path',
            'processing_result',
            'editor_session'
        ]
        
        for key in keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]
        
        # 清理临时文件
        self.ai_engine.cleanup_temp_files(older_than_hours=1)
