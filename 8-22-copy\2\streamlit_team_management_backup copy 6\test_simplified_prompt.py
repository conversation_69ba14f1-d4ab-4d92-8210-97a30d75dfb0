#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化提示词队徽生成
Test Simplified Prompt Logo Generation

验证使用简化提示词的队徽生成效果
"""

import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

def test_simplified_prompt():
    """测试简化提示词队徽生成"""
    print("🧪 测试简化提示词队徽生成...")
    print("=" * 50)
    
    try:
        from services.team_logo_generator import team_logo_generator
        
        # 测试球队
        test_teams = [
            {"name": "雄鹰足球俱乐部", "style": "现代", "color": "红色和金色"},
            {"name": "闪电足球队", "style": "传统", "color": "蓝色和白色"}
        ]
        
        for team in test_teams:
            print(f"\n🏆 测试球队: {team['name']}")
            print(f"   风格: {team['style']}")
            print(f"   颜色: {team['color']}")
            print("-" * 40)
            
            # 生成队徽
            result = team_logo_generator.generate_team_logo_complete(
                team_name=team['name'],
                team_style=team['style'],
                color_preference=team['color']
            )
            
            if result.get("success"):
                print("✅ 队徽生成成功！")
                print(f"   描述长度: {len(result.get('description', ''))}")
                print(f"   有图像: {result.get('has_image', False)}")
                print(f"   文件路径: {result.get('image_path', 'N/A')}")
                
                # 显示描述的前200字符
                description = result.get('description', '')
                if description:
                    print(f"   描述预览: {description[:200]}...")
                
            else:
                print(f"❌ 队徽生成失败: {result.get('error', '未知错误')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_auto_generation():
    """测试自动队徽生成功能"""
    print("\n🧪 测试自动队徽生成功能...")
    print("=" * 50)
    
    try:
        from services.enhanced_ai_service import enhanced_ai_service
        
        if not enhanced_ai_service.is_available():
            print("❌ AI服务不可用，跳过测试")
            return False
        
        # 模拟用户输入
        test_text = "我们是火焰战士足球队，队长是张三，球衣是橙色和黑色的。"
        
        print(f"📝 测试文本: {test_text}")
        
        # 调用信息提取方法（应该自动生成队徽）
        arguments = {"user_text": test_text}
        result = enhanced_ai_service._extract_team_info_from_text(arguments)
        
        print(f"📊 提取结果成功: {result.get('success', False)}")
        
        # 检查是否自动生成了队徽
        extracted_info = result.get("extracted_info", {})
        auto_logo = extracted_info.get("auto_generated_logo")
        
        if auto_logo:
            print("✅ 自动队徽生成成功！")
            print(f"   球队名称: {auto_logo.get('team_name')}")
            print(f"   触发方式: {auto_logo.get('auto_trigger')}")
            print(f"   有图像: {auto_logo.get('has_image', False)}")
        else:
            print("❌ 没有自动生成队徽")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def show_prompt_details():
    """显示当前使用的提示词详情"""
    print("\n📋 当前使用的提示词方案")
    print("=" * 50)
    
    print("🎨 GPT-4o 描述生成提示词:")
    print("""
    请为足球队"{team_name}"设计一个队徽描述。

    要求：
    - 风格：{team_style}
    - 颜色偏好：{color_preference}
    - 适合足球队使用
    - 简洁明了，易于识别
    - 体现团队精神

    请提供详细的设计描述，包括：
    1. 主要图案元素
    2. 颜色搭配
    3. 整体布局
    4. 寓意说明
    """)
    
    print("\n🖼️ DALL-E 3 图像生成提示词:")
    print("""
    Design a football team logo: {description}
    """)
    
    print("\n🔧 系统提示词:")
    print("你是一个专业的队徽设计师，擅长为足球队设计有意义的队徽。")
    
    print("\n⚙️ 参数设置:")
    print("- 模型: gpt-4o")
    print("- 温度: 0.8")
    print("- 图像尺寸: 1024x1024")
    print("- 图像质量: standard")

def main():
    """主测试函数"""
    print("🚀 开始测试简化提示词队徽生成功能...")
    print("=" * 60)
    
    # 显示提示词详情
    show_prompt_details()
    
    # 运行测试
    tests = [
        ("简化提示词队徽生成", test_simplified_prompt),
        ("自动队徽生成功能", test_auto_generation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 简化提示词测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！简化提示词队徽生成功能正常工作！")
        print("\n💡 提示词特点:")
        print("   ✅ 简洁高效 - 4个基本设计要求")
        print("   ✅ 平衡质量 - 保持专业性的同时提高效率")
        print("   ✅ 易于维护 - 提示词结构清晰简单")
        print("   ✅ 成本优化 - 减少token使用，降低API费用")
    else:
        print(f"\n⚠️ 还有 {total-passed} 个问题需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    main()
