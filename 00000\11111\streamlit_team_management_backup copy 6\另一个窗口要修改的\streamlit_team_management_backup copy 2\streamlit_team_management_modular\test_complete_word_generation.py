#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整Word生成功能测试
端到端测试Word生成功能，验证最终生成的文档是否符合模板要求
"""

import sys
import os
from pathlib import Path
import json
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_template_exists():
    """测试模板文件是否存在"""
    print("📋 检查Word模板文件...")
    print("=" * 50)
    
    template_path = "../word_zc/ai-football-generator/template.docx"
    
    if os.path.exists(template_path):
        file_size = os.path.getsize(template_path)
        print(f"✅ 模板文件存在: {template_path}")
        print(f"📏 模板大小: {file_size / 1024:.1f} KB")
        return True
    else:
        print(f"❌ 模板文件不存在: {template_path}")
        return False

def test_photos_exist():
    """测试球员照片是否存在"""
    print("\n📸 检查球员照片文件...")
    print("=" * 50)
    
    photos_dir = "../word_zc/ai-football-generator/photos"
    required_photos = ["player1.png", "player2.jpg", "player3.jpg", "player4.jpg", "player5.jpg"]
    
    existing_photos = []
    for photo in required_photos:
        photo_path = os.path.join(photos_dir, photo)
        if os.path.exists(photo_path):
            file_size = os.path.getsize(photo_path)
            print(f"✅ {photo}: {file_size / 1024:.1f} KB")
            existing_photos.append(photo)
        else:
            print(f"❌ {photo}: 不存在")
    
    print(f"\n📊 找到 {len(existing_photos)}/{len(required_photos)} 张照片")
    return existing_photos

def create_realistic_test_data(existing_photos):
    """创建真实的测试数据"""
    print("\n📝 创建真实测试数据...")
    print("=" * 50)
    
    # 真实的球队信息
    team_data = {
        'name': '淄川区太河镇足球队',
        'leader': '张建国',
        'coach': '李明华',
        'doctor': '王医生'
    }
    
    # 真实的球员信息
    realistic_players = [
        {'name': '张雷', 'jersey_number': '10', 'position': '前锋'},
        {'name': '李明', 'jersey_number': '9', 'position': '中锋'},
        {'name': '王强', 'jersey_number': '8', 'position': '中场'},
        {'name': '赵飞', 'jersey_number': '7', 'position': '后卫'},
        {'name': '刘洋', 'jersey_number': '6', 'position': '守门员'},
        {'name': '陈浩', 'jersey_number': '5', 'position': '后卫'},
        {'name': '孙伟', 'jersey_number': '4', 'position': '中场'},
        {'name': '周杰', 'jersey_number': '3', 'position': '后卫'},
        {'name': '吴磊', 'jersey_number': '2', 'position': '后卫'},
        {'name': '郑凯', 'jersey_number': '1', 'position': '替补守门员'}
    ]
    
    # 为球员分配现有的照片
    players_data = []
    photos_dir = "../word_zc/ai-football-generator/photos"
    
    for i, player in enumerate(realistic_players):
        if i < len(existing_photos):
            photo_path = os.path.join(photos_dir, existing_photos[i])
        else:
            photo_path = ""  # 没有照片的球员
        
        players_data.append({
            'name': player['name'],
            'jersey_number': player['jersey_number'],
            'photo': photo_path
        })
    
    print(f"✅ 创建了球队: {team_data['name']}")
    print(f"✅ 创建了 {len(players_data)} 名球员")
    print(f"✅ 其中 {len(existing_photos)} 名球员有照片")
    
    return team_data, players_data

def test_json_generation(team_data, players_data):
    """测试JSON数据生成"""
    print("\n🔧 测试JSON数据生成...")
    print("=" * 50)
    
    try:
        # 构建完整的JSON数据
        json_data = {
            "teamInfo": {
                "title": f"2025年五人制足球比赛报名表",
                "organizationName": team_data['name'],
                "teamLeader": team_data['leader'],
                "coach": team_data['coach'],
                "teamDoctor": team_data['doctor']
            },
            "players": [
                {
                    "number": player['jersey_number'],
                    "name": player['name'],
                    "photoPath": player['photo']
                }
                for player in players_data
            ],
            "config": {
                "templatePath": "template.docx",
                "outputDir": "output",
                "photosDir": "photos"
            },
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "test_type": "complete_integration_test",
                "team_name": team_data['name'],
                "player_count": len(players_data)
            }
        }
        
        # 保存测试JSON文件
        test_json_path = "../word_zc/ai-football-generator/test_complete_data.json"
        with open(test_json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ JSON数据生成成功")
        print(f"📁 保存位置: {test_json_path}")
        print(f"📊 球队: {json_data['teamInfo']['organizationName']}")
        print(f"📊 球员: {len(json_data['players'])}人")
        
        return test_json_path
        
    except Exception as e:
        print(f"❌ JSON数据生成失败: {e}")
        return None

def test_java_word_generation(json_file_path):
    """测试Java Word生成"""
    print("\n☕ 测试Java Word生成...")
    print("=" * 50)
    
    try:
        import subprocess
        
        jar_path = "../word_zc/ai-football-generator/target/word-generator.jar"
        
        # 执行Java命令
        cmd = ['java', '-jar', jar_path, json_file_path]
        
        print(f"🚀 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='ignore',
            timeout=120  # 2分钟超时
        )
        
        print(f"📊 返回码: {result.returncode}")
        
        if result.returncode == 0:
            # 查找SUCCESS行
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                line = line.strip()
                if line.startswith('SUCCESS:'):
                    file_path = line[8:].strip()
                    print(f"✅ Word生成成功!")
                    print(f"📁 文件路径: {file_path}")
                    return file_path
            
            print("⚠️ 未找到SUCCESS输出，但返回码为0")
            print("📄 标准输出:")
            print(result.stdout)
            return None
        else:
            print(f"❌ Java程序执行失败")
            print("📄 错误输出:")
            print(result.stderr)
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ Java程序执行超时")
        return None
    except Exception as e:
        print(f"❌ Java程序执行异常: {e}")
        return None

def test_python_word_service(team_data, players_data):
    """测试Python Word生成服务"""
    print("\n🐍 测试Python Word生成服务...")
    print("=" * 50)
    
    try:
        from word_generator_service import WordGeneratorService
        
        # 初始化服务
        jar_path = "../word_zc/ai-football-generator/target/word-generator.jar"
        template_path = "../word_zc/ai-football-generator/template.docx"
        output_dir = "word_output"
        
        word_service = WordGeneratorService(jar_path, template_path, output_dir)
        print("✅ Word生成服务初始化成功")
        
        # 生成Word报名表
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print("✅ Python Word生成成功!")
            print(f"📁 文件路径: {result['file_path']}")
            print(f"📊 球队: {result.get('team_name', 'N/A')}")
            print(f"👥 球员: {result.get('player_count', 'N/A')}人")
            return result['file_path']
        else:
            print("❌ Python Word生成失败")
            print(f"错误: {result['message']}")
            if 'error' in result:
                print(f"详情: {result['error']}")
            return None
            
    except Exception as e:
        print(f"❌ Python Word服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_generated_file(file_path):
    """分析生成的Word文件"""
    print(f"\n📊 分析生成的Word文件...")
    print("=" * 50)
    
    try:
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return False
        
        # 文件基本信息
        file_size = os.path.getsize(file_path)
        file_name = os.path.basename(file_path)
        
        print(f"✅ 文件存在: {file_name}")
        print(f"📏 文件大小: {file_size / 1024:.1f} KB")
        
        # 检查文件大小是否合理
        if file_size < 50 * 1024:  # 小于50KB可能有问题
            print("⚠️ 文件大小较小，可能生成不完整")
        elif file_size > 10 * 1024 * 1024:  # 大于10MB可能有问题
            print("⚠️ 文件大小较大，可能有异常")
        else:
            print("✅ 文件大小正常")
        
        # 检查文件扩展名
        if file_path.endswith('.docx'):
            print("✅ 文件格式正确 (.docx)")
        else:
            print("⚠️ 文件格式可能不正确")
        
        # 尝试验证是否为有效的Word文档
        try:
            import zipfile
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                file_list = zip_file.namelist()
                if 'word/document.xml' in file_list:
                    print("✅ Word文档结构正确")
                else:
                    print("⚠️ Word文档结构可能有问题")
        except:
            print("⚠️ 无法验证Word文档结构")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件分析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 完整Word生成功能端到端测试")
    print("=" * 70)
    print("验证Word生成功能是否能真正生成符合模板的文档")
    print("=" * 70)
    
    # 测试步骤
    steps = [
        ("检查模板文件", test_template_exists),
        ("检查球员照片", test_photos_exist),
    ]
    
    # 执行前置检查
    template_ok = test_template_exists()
    existing_photos = test_photos_exist()
    
    if not template_ok:
        print("\n❌ 模板文件缺失，无法继续测试")
        return
    
    if not existing_photos:
        print("\n⚠️ 没有找到球员照片，将生成无照片的报名表")
    
    # 创建测试数据
    team_data, players_data = create_realistic_test_data(existing_photos)
    
    # 测试JSON生成
    json_file = test_json_generation(team_data, players_data)
    
    # 测试Java直接调用
    java_result = None
    if json_file:
        java_result = test_java_word_generation(json_file)
    
    # 测试Python服务调用
    python_result = test_python_word_service(team_data, players_data)
    
    # 分析结果
    print("\n" + "=" * 70)
    print("📊 测试结果总结")
    print("=" * 70)
    
    success_count = 0
    total_tests = 2
    
    if java_result:
        print("✅ Java直接调用: 成功")
        if analyze_generated_file(java_result):
            success_count += 1
    else:
        print("❌ Java直接调用: 失败")
    
    if python_result:
        print("✅ Python服务调用: 成功")
        if analyze_generated_file(python_result):
            success_count += 1
    else:
        print("❌ Python服务调用: 失败")
    
    print(f"\n📈 成功率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if success_count == total_tests:
        print("\n🎉 所有测试通过！Word生成功能完全正常！")
        print("\n✅ 验证结果:")
        print("   1. 模板文件正确加载")
        print("   2. 球员照片正确嵌入")
        print("   3. 球队信息正确填充")
        print("   4. Word文档格式正确")
        print("   5. Java和Python调用都成功")
        print("\n🎯 功能已准备就绪，可以投入使用！")
        
        # 显示生成的文件
        if python_result:
            print(f"\n📁 最新生成的文件: {python_result}")
            print("💡 您可以打开这个文件查看生成效果")
            
    elif success_count > 0:
        print(f"\n⚠️ 部分测试通过 ({success_count}/{total_tests})")
        print("建议检查失败的部分并进行修复")
    else:
        print("\n❌ 所有测试失败")
        print("请检查Java环境、模板文件和配置")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
